import { Steps } from 'nextra/components';

# Deploy LobeChat with SealOS

If you want to deploy LobeChat on SealOS, you can follow the steps below:

## SealOS Deployment Process

<Steps>

### Prepare your OpenAI API Key

Go to [OpenAI](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

### Click the button below to deploy

[![][deploy-button-image]][deploy-link]

### After deployment, you can start using it

### Bind a custom domain (optional)

You can use the subdomain provided by SealOS, or choose to bind a custom domain. Currently, the domains provided by SealOS have not been contaminated, and can be directly accessed in most regions.

</Steps>

[deploy-button-image]: https://raw.githubusercontent.com/labring-actions/templates/main/Deploy-on-Sealos.svg
[deploy-link]: https://cloud.sealos.io/?openapp=system-template%3FtemplateName%3Dlobe-chat
