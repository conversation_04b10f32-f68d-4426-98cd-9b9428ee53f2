import { Steps } from 'nextra/components';

# Deploy LobeChat with Zeabur

If you want to deploy LobeChat on Zeabur, you can follow the steps below:

## Zeabur Deployment Process

<Steps>

### Prepare your OpenAI API Key

Go to [OpenAI API Key](https://platform.openai.com/account/api-keys) to get your OpenAI API Key.

### Click the button below to deploy

[![][deploy-button-image]][deploy-link]

### Once deployed, you can start using it

### Bind a custom domain (optional)

You can use the subdomain provided by Zeabur, or choose to bind a custom domain. Currently, the domains provided by Zeabur have not been contaminated, and most regions can connect directly.

</Steps>

[deploy-button-image]: https://zeabur.com/button.svg
[deploy-link]: https://zeabur.com/templates/VZGGTI
