import { Callout, Steps, Tabs } from 'nextra/components';

# Docker Compose Deployment Guide

[![][docker-release-shield]][docker-release-link][![][docker-size-shield]][docker-size-link][![][docker-pulls-shield]][docker-pulls-link]

We provide a [Docker image][docker-release-link] for deploying the LobeChat service on your private device.

<Steps>

### Install Docker Container Environment

(Skip this step if already installed)

<Tabs items={['Ubuntu', 'CentOS']}>
<Tabs.Tab>

```fish
$ apt install docker.io
```

</Tabs.Tab>
<Tabs.Tab>

```fish
$ yum install docker
```

</Tabs.Tab>

</Tabs>

### Run Docker Compose Deployment Command

When using `docker-compose`, the configuration file is as follows:

```yml
version: '3.8'

services:
  lobe-chat:
    image: lobehub/lobe-chat
    container_name: lobe-chat
    restart: always
    ports:
      - '3210:3210'
    environment:
      OPENAI_API_KEY: sk-xxxx
      OPENAI_PROXY_URL: https://api-proxy.com/v1
      ACCESS_CODE: lobe66
```

Run the following command to start the Lobe Chat service:

```bash
$ docker-compose up -d
```

### Crontab Automatic Update Script (Optional)

Similarly, you can use the following script to automatically update Lobe Chat. When using `Docker Compose`, no additional configuration of environment variables is required.

```bash
#!/bin/bash
# auto-update-lobe-chat.sh

# Set proxy (optional)
export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890

# Pull the latest image and store the output in a variable
output=$(docker pull lobehub/lobe-chat:latest 2>&1)

# Check if the pull command was executed successfully
if [ $? -ne 0 ]; then
  exit 1
fi

# Check if the output contains a specific string
echo "$output" | grep -q "Image is up to date for lobehub/lobe-chat:latest"

# If the image is already up to date, do nothing
if [ $? -eq 0 ]; then
  exit 0
fi

echo "Detected Lobe-Chat update"

# Remove the old container
echo "Removed: $(docker rm -f Lobe-Chat)"

# You may need to navigate to the directory where `docker-compose.yml` is located first
# cd /path/to/docker-compose-folder

# Run the new container
echo "Started: $(docker-compose up)"

# Print the update time and version
echo "Update time: $(date)"
echo "Version: $(docker inspect lobehub/lobe-chat:latest | grep 'org.opencontainers.image.version' | awk -F'"' '{print $4}')"

# Clean up unused images
docker images | grep 'lobehub/lobe-chat' | grep -v 'latest' | awk '{print $3}' | xargs -r docker rmi > /dev/null 2>&1
echo "Removed old images."
```

This script can also be used in Crontab, but ensure that your Crontab can find the correct Docker command. It is recommended to use absolute paths.

Configure Crontab to execute the script every 5 minutes:

```bash
*/5 * * * * /path/to/auto-update-lobe-chat.sh >> /path/to/auto-update-lobe-chat.log 2>&1
```

</Steps>

[docker-pulls-link]: https://hub.docker.com/r/lobehub/lobe-chat
[docker-pulls-shield]: https://img.shields.io/docker/pulls/lobehub/lobe-chat?color=45cc11&labelColor=black&style=flat-square
[docker-release-link]: https://hub.docker.com/r/lobehub/lobe-chat
[docker-release-shield]: https://img.shields.io/docker/v/lobehub/lobe-chat?color=369eff&label=docker&labelColor=black&logo=docker&logoColor=white&style=flat-square
[docker-size-link]: https://hub.docker.com/r/lobehub/lobe-chat
[docker-size-shield]: https://img.shields.io/docker/image-size/lobehub/lobe-chat?color=369eff&labelColor=black&style=flat-square
