# 与 Ollama 集成

Ollama 是一款强大的本地运行大型语言模型（LLM）的框架，支持多种语言模型，包括 Llama 2, Mistral 等。现在，LobeChat 已经支持与 Ollama 的集成，这意味着你可以在 LobeChat 中轻松使用 Ollama 提供的语言模型来增强你的应用。

本文档将指导你如何配置与部署 LobeChat 来使用 Ollama：

## 本地启动 Ollama

首先，你需要安装 Ollama，安装与配置 Ollama 的详细步骤可以参考 [Ollama 官方站点](https://ollama.com)。

## 本地运行 LobeChat

假设你已经在本地 `11434` 端口启动了 Ollama 服务。运行以下 Docker 命令行，在本地启动 LobeChat：

```bash
docker run -d -p 3210:3210 -e OLLAMA_PROXY_URL=http://host.docker.internal:11434/v1 lobehub/lobe-chat
```

接下来，你就可以使用 LobeChat 与本地 LLM 对话了。

关于在 LobeChat 中使用 Ollama 的更多信息，请查阅 [Ollama 使用](/zh/usage/providers/ollama)。
