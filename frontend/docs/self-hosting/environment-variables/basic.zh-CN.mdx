import { Callout } from 'nextra/components';

# 环境变量

LobeChat 在部署时提供了一些额外的配置项，你可以使用环境变量进行自定义设置。

## 通用变量

### `ACCESS_CODE`

- 类型：可选
- 描述：添加访问 LobeChat 服务的密码，你可以设置一个长密码以防被爆破
- 默认值：-
- 示例：`awCTe)re_r74` or `rtrt_ewee3@09!`

### `ENABLE_OAUTH_SSO`

- 类型：可选
- 描述：为 LobeChat 启用单点登录 (SSO)。设置为 `1` 以启用单点登录。有关详细信息，请参阅[身份验证服务](#身份验证服务)。
- 默认值: `-`
- 示例: `1`

### `NEXT_PUBLIC_BASE_PATH`

- 类型：可选
- 描述：为 LobeChat 添加 `basePath`
- 默认值: `-`
- 示例: `/test`

#### `DEFAULT_AGENT_CONFIG`

- 类型：可选
- 描述：用于配置 LobeChat 默认助理的默认配置。它支持多种数据类型和结构，包括键值对、嵌套字段、数组值等。
- 默认值：`-`
- 示例：`'model=gpt-4-1106-preview;params.max_tokens=300;plugins=search-engine,lobe-image-designer`

`DEFAULT_AGENT_CONFIG` 用于配置 LobeChat 默认助理的默认配置。它支持多种数据类型和结构，包括键值对、嵌套字段、数组值等。下表详细说明了 `DEFAULT_AGENT_CONFIG` 环境变量的配置项、示例以及相应解释：

| 配置项类型 | 示例 | 解释 |
| --- | --- | --- |
| 基本键值对 | `model=gpt-4` | 设置模型为 `gpt-4`。 |
| 嵌套字段 | `tts.sttLocale=en-US` | 设置文本到语音服务的语言区域为 `en-US`。 |
| 数组 | `plugins=search-engine,lobe-image-designer` | 启用 `search-engine` 和 `lobe-image-designer` 插件。 |
| 中文逗号 | `plugins=search-engine，lobe-image-designer` | 同上，演示支持中文逗号分隔。 |
| 多个配置项 | `model=glm-4;provider=zhipu` | 设置模型为 `glm-4` 且模型服务商为 `zhipu`。 |
| 数字值 | `params.max_tokens=300` | 设置最大令牌数为 `300`。 |
| 布尔值 | `enableAutoCreateTopic=true` | 启用自动创建主题。 |
| 特殊字符 | `inputTemplate="Hello; I am a bot;"` | 设置输入模板为 `Hello; I am a bot;`。 |
| 错误处理 | `model=gpt-4;maxToken` | 忽略无效条目 `maxToken`，仅解析出 `model=gpt-4`。 |
| 值覆盖 | `model=gpt-4;model=gpt-4-1106-preview` | 如果键重复，使用最后一次出现的值，此处 `model` 的值为 `gpt-4-1106-preview`。 |

相关阅读：

- [\[RFC\] 022 - 环境变量配置默认助手参数](https://github.com/lobehub/lobe-chat/discussions/913)

## 身份验证服务

### 通用设置

#### `ENABLE_OAUTH_SSO`

- 类型：必选
- 描述：为 LobeChat 启用单点登录 (SSO)。设置为 `1` 以启用单点登录。
- 默认值: `-`
- 示例: `1`

#### `NEXTAUTH_SECRET`

- 类型：必选
- 描述：用于加密 Auth.js 会话令牌的密钥。您可以使用以下命令生成秘钥： `openssl rand -base64 32`.
- 默认值: `-`
- 示例: `Tfhi2t2pelSMEA8eaV61KaqPNEndFFdMIxDaJnS1CUI=`

#### `NEXTAUTH_URL`

- 类型：可选
- 描述：该URL用于指定Auth.js在执行OAuth验证时的回调地址，在Vercel上部署时无需设置。
- 默认值：`-`
- 示例：`https://example.com/api/auth`

### Auth0

<Callout>
  目前我们只支持 Auth0 身份验证服务提供商。如果您需要使用其他身份验证服务提供商，可以提交
  [功能请求](https://github.com/lobehub/lobe-chat/issues/new/choose) 或 Pull Request。
</Callout>

#### `AUTH0_CLIENT_ID`

- 类型：必选
- 描述: Auth0 应用程序的 Client ID，您可以访问[这里][auth0-client-page]并导航至应用程序设置来查看
- 默认值: `-`
- 示例: `evCnOJP1UX8FMnXR9Xkj5t0NyFn5p70P`

#### `AUTH0_CLIENT_SECRET`

- 类型：必选
- 描述: Auth0 应用程序的 Client Secret
- 默认值: `-`
- 示例: `****************************************************************`

#### `AUTH0_ISSUER`

- 类型：必选
- 描述: Auth0 应用程序的签发人 / 域
- 默认值: `-`
- 示例: `https://example.auth0.com`

## 插件服务

### `PLUGINS_INDEX_URL`

- 类型：可选
- 描述：LobeChat 插件市场的索引地址，如果你自行部署了插件市场的服务，可以使用该变量来覆盖默认的插件市场地址
- 默认值：`https://chat-plugins.lobehub.com`

### `PLUGIN_SETTINGS`

- 类型：可选
- 描述：用于配置插件的设置，使用 `插件名:设置字段=设置值` 的格式来配置插件的设置，多个设置字段用英文分号 `;` 隔开，多个插件设置使用英文逗号`,`隔开。
- 默认值：`-`
- 示例：`search-engine:SERPAPI_API_KEY=xxxxx,plugin-2:key1=value1;key2=value2`

上述示例表示设置 `search-engine` 插件的 `SERPAPI_API_KEY` 为 `xxxxx`，设置 `plugin-2` 的 `key1` 为 `value1`，`key2` 为 `value2`。生成的插件设置配置如下：

```json
{
  "plugin-2": {
    "key1": "value1",
    "key2": "value2"
  },
  "search-engine": {
    "SERPAPI_API_KEY": "xxxxx"
  }
}
```

## 助手市场

### `AGENTS_INDEX_URL`

- 类型：可选
- 描述：LobeChat 助手市场的索引地址，如果你自行部署了助手市场的服务，可以使用该变量来覆盖默认的市场地址
- 默认值：`https://chat-agents.lobehub.com`

## 数据统计

### Vercel Analytics

#### `NEXT_PUBLIC_ANALYTICS_VERCEL`

- 类型：可选
- 描述：用于配置 Vercel Analytics 的环境变量，当设为 `1` 时开启 Vercel Analytics
- 默认值： `-`
- 示例：`1`

#### `NEXT_PUBLIC_VERCEL_DEBUG`

- 类型：可选
- 描述：用于开启 Vercel Analytics 的调试模式
- 默认值： `-`
- 示例：`1`

### Posthog Analytics

#### `NEXT_PUBLIC_ANALYTICS_POSTHOG`

- 类型：可选
- 描述：用于开启 [PostHog Analytics][posthog-analytics-url] 的环境变量，设为 `1` 时开启 PostHog Analytics
- 默认值： `-`
- 示例：`1`

#### `NEXT_PUBLIC_POSTHOG_KEY`

- 类型：可选
- 描述：设置 PostHog 项目 Key
- 默认值： `-`
- 示例：`phc_xxxxxxxx`

#### `NEXT_PUBLIC_POSTHOG_HOST`

- 类型：可选
- 描述：设置 PostHog 服务的部署地址，默认为官方的 SAAS 地址
- 默认值：`https://app.posthog.com`
- 示例：`https://example.com`

#### `NEXT_PUBLIC_POSTHOG_DEBUG`

- 类型：可选
- 描述：开启 PostHog 的调试模式
- 默认值： `-`
- 示例：`1`

### Umami Analytics

#### `NEXT_PUBLIC_ANALYTICS_UMAMI`

- 类型：可选
- 描述：用于开启 [Umami Analytics][umami-analytics-url] 的环境变量，设为 `1` 时开启 Umami Analytics
- 默认值： `-`
- 示例：`1`

#### `NEXT_PUBLIC_UMAMI_SCRIPT_URL`

- 类型：可选
- 描述：Umami 脚本的网址，默认为 Umami Cloud 提供的脚本网址
- 默认值：`https://analytics.umami.is/script.js`
- 示例：`https://umami.your-site.com/script.js`

#### `NEXT_PUBLIC_UMAMI_WEBSITE_ID`

- 类型：必选
- 描述：你的 Umami 的 Website ID
- 默认值：`-`
- 示例：`E738D82A-EE9E-4806-A81F-0CA3CAE57F65`

[auth0-client-page]: https://manage.auth0.com/dashboard
[azure-api-verion-url]: https://docs.microsoft.com/zh-cn/azure/developer/javascript/api-reference/es-modules/azure-sdk/ai-translation/translationconfiguration?view=azure-node-latest#api-version
[openai-api-page]: https://platform.openai.com/account/api-keys
[posthog-analytics-url]: https://posthog.com
[umami-analytics-url]: https://umami.is
