import { Callout } from 'nextra/components';

# 模型服务商

LobeChat 在部署时提供了丰富的模型服务商相关的环境变量，你可以使用这些环境变量轻松定义需要在 LobeChat 中开启的模型服务商。

## OpenAI

### `OPENAI_API_KEY`

- 类型：必选
- 描述：这是你在 OpenAI 账户页面申请的 API 密钥，可以前往[这里][openai-api-page]查看
- 默认值：-
- 示例：`sk-xxxxxx...xxxxxx`

### `OPENAI_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 OpenAI 接口代理，可以使用此配置项来覆盖默认的 OpenAI API 请求基础 URL
- 默认值：`https://api.openai.com/v1`
- 示例：`https://api.chatanywhere.cn` 或 `https://aihubmix.com/v1`

<Callout type={'warning'}>
  请检查你的代理服务商的请求后缀，有的代理服务商会在请求后缀添加
  `/v1`，有的则不会。如果你在测试时发现 AI 返回的消息为空，请尝试添加 `/v1` 后缀后重试。
</Callout>

<Callout type={'info'}>
  是否填写 `/v1` 跟模型服务商有很大关系，比如 openai 的默认地址是 `api.openai.com/v1`
  。如果你的代理商转发了 `/v1` 这个接口，那么直接填 `proxy.com` 即可。 但如果模型服务商是直接转发了
  `api.openai.com` 域名，那么你就要自己加上 `/v1` 这个 url。
</Callout>

相关讨论：

- [Docker 安装，配置好环境变量后，为何返回值是空白？](https://github.com/lobehub/lobe-chat/discussions/623)
- [使用第三方接口报错的原因](https://github.com/lobehub/lobe-chat/discussions/734)
- [代理服务器地址填了聊天没任何反应](https://github.com/lobehub/lobe-chat/discussions/1065)

### `CUSTOM_MODELS`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名` 来自定义模型的展示名，用英文逗号隔开。
- 默认值：`-`
- 示例：`+qwen-7b-chat,+glm-6b,-gpt-3.5-turbo,gpt-4-0125-preview=gpt-4-turbo`

上面示例表示增加 `qwen-7b-chat` 和 `glm-6b` 到模型列表，而从列表中删除 `gpt-3.5-turbo`，并将 `gpt-4-0125-preview` 模型名字展示为 `gpt-4-turbo`。如果你想先禁用所有模型，再启用指定模型，可以使用 `-all,+gpt-3.5-turbo`，则表示仅启用 `gpt-3.5-turbo`。

你可以在 [modelProviders](https://github.com/lobehub/lobe-chat/tree/main/src/config/modelProviders) 查找到当前的所有模型名。

### `API_KEY_SELECT_MODE`

- 类型：可选
- 描述：用于控制多个API Keys时，选择Key的模式，当前支持 `random` 和 `turn`
- 默认值：`random`
- 示例：`random` 或 `turn`

使用 `random` 模式下，将在多个API Keys中随机获取一个API Key。

使用 `turn` 模式下，将按照填写的顺序，轮训获取得到API Key。

## Azure OpenAI

如果你需要使用 Azure OpenAI 来提供模型服务，可以查阅 [使用 Azure OpenAI 部署](../Deployment/Deploy-with-Azure-OpenAI.zh-CN.md) 章节查看详细步骤，这里将列举和 Azure OpenAI 相关的环境变量。

### `USE_AZURE_OPENAI`

- 类型：可选
- 描述：设置该值为 `1` 开启 Azure OpenAI 配置
- 默认值：-
- 示例：`1`

### `AZURE_API_KEY`

- 类型：可选
- 描述：这是你在 Azure OpenAI 账户页面申请的 API 密钥
- 默认值：-
- 示例：`c55168be3874490ef0565d9779ecd5a6`

### `AZURE_API_VERSION`

- 类型：可选
- 描述：Azure 的 API 版本，遵循 YYYY-MM-DD 格式
- 默认值：`2023-08-01-preview`
- 示例：`2023-05-15`，查阅[最新版本][azure-api-verion-url]

## 智谱 AI

### `ZHIPU_API_KEY`

- 类型：必选
- 描述：这是你在 智谱 AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`4582d332441a313f5c2ed9824d1798ca.rC8EcTAhgbOuAuVT`

## Moonshot AI

### `MOONSHOT_API_KEY`

- 类型：必选
- 描述：这是你在 Moonshot AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`Y2xpdGhpMzNhZXNoYjVtdnZjMWc6bXNrLWIxQlk3aDNPaXpBWnc0V1RaMDhSRmRFVlpZUWY=`

## Google AI

### `GOOGLE_API_KEY`

- 类型：必选
- 描述：这是你在 Google AI Platform 申请的 API 密钥，用于访问 Google AI 服务
- 默认值：-
- 示例：`AIraDyDwcw254kwJaGjI9wwaHcdDCS__Vt3xQE`

## AWS Bedrock

### `AWS_ACCESS_KEY_ID`

- 类型：必选
- 描述：用于 AWS 服务认证的访问键 ID
- 默认值：-
- 示例：`********************`

### `AWS_SECRET_ACCESS_KEY`

- 类型：必选
- 描述：用于 AWS 服务认证的密钥
- 默认值：-
- 示例：`Th3vXxLYpuKcv2BARktPSTPxx+jbSiFT6/0w7oEC`

### `AWS_REGION`

- 类型：可选
- 描述：AWS 服务的区域设置
- 默认值：`us-east-1`
- 示例：`us-east-1`

## Ollama

### `OLLAMA_PROXY_URL`

- 类型：可选
- 描述：用于启用 Ollama 服务，设置后可在语言模型列表内展示可选开源语言模型，也可以指定自定义语言模型
- 默认值：-
- 示例：`http://127.0.0.1:11434/v1`

## Perplexity AI

### `PERPLEXITY_API_KEY`

- 类型：必选
- 描述：这是你在 Perplexity AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`pplx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## Anthropic AI

### `ANTHROPIC_API_KEY`

- 类型：必选
- 描述：这是你在 Anthropic AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`sk-ant-apixx-xxxxxxxxx-xxxxxxxxxxxxxxxxx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-xxxxxxxx`

## Mistral AI

### `MISTRAL_API_KEY`

- 类型：必选
- 描述：这是你在 Mistral AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx=`
