import { Callout } from 'nextra/components';

# Environment Variables

LobeChat provides some additional configuration options during deployment, which can be customized using environment variables.

## Common Variables

### `ACCESS_CODE`

- Type: Optional
- Description: Add a password to access the LobeChat service. You can set a long password to prevent brute force attacks.
- Default: -
- Example: `awCTe)re_r74` or `rtrt_ewee3@09!`

### `ENABLE_OAUTH_SSO`

- Type: Optional
- Description: Enable Single Sign-On (SSO) for LobeChat. Set to `1` to enable SSO. For more information, see [Authentication Services](#authentication-services).
- Default: -
- Example: `1`

### `NEXT_PUBLIC_BASE_PATH`

- Type: Optional
- Description: Add a `basePath` for LobeChat.
- Default: -
- Example: `/test`

### `DEFAULT_AGENT_CONFIG`

- Type: Optional
- Description: Used to configure the default settings for the LobeChat default agent. It supports various data types and structures, including key-value pairs, nested fields, array values, and more.
- Default: -
- Example: `'model=gpt-4-1106-preview;params.max_tokens=300;plugins=search-engine,lobe-image-designer`

The `DEFAULT_AGENT_CONFIG` is used to configure the default settings for the LobeChat default agent. It supports various data types and structures, including key-value pairs, nested fields, array values, and more. The table below provides detailed information on the configuration options, examples, and corresponding explanations for the `DEFAULT_AGENT_CONFIG` environment variable:

| Configuration Type | Example | Explanation |
| --- | --- | --- |
| Basic Key-Value Pair | `model=gpt-4` | Set the model to `gpt-4`. |
| Nested Field | `tts.sttLocale=en-US` | Set the language locale for the text-to-speech service to `en-US`. |
| Array | `plugins=search-engine,lobe-image-designer` | Enable the `search-engine` and `lobe-image-designer` plugins. |
| Chinese Comma | `plugins=search-engine，lobe-image-designer` | Same as above, demonstrating support for Chinese comma separation. |
| Multiple Configurations | `model=glm-4;provider=zhipu` | Set the model to `glm-4` and the model provider to `zhipu`. |
| Numeric Value | `params.max_tokens=300` | Set the maximum tokens to `300`. |
| Boolean Value | `enableAutoCreateTopic=true` | Enable automatic topic creation. |
| Special Characters | `inputTemplate="Hello; I am a bot;"` | Set the input template to `Hello; I am a bot;`. |
| Error Handling | `model=gpt-4;maxToken` | Ignore invalid entry `maxToken` and only parse `model=gpt-4`. |
| Value Override | `model=gpt-4;model=gpt-4-1106-preview` | If a key is repeated, use the value that appears last; in this case, the value of `model` is `gpt-4-1106-preview`. |

Further reading:

- [\[RFC\] 022 - Default Assistant Parameters Configuration via Environment Variables](https://github.com/lobehub/lobe-chat/discussions/913)

## Identity Verification Service

### General Settings

#### `ENABLE_OAUTH_SSO`

- Type: Required
- Description: Enable single sign-on (SSO) for LobeChat. Set to `1` to enable single sign-on.
- Default: `-`
- Example: `1`

#### `NEXTAUTH_SECRET`

- Type: Required
- Description: Key used to encrypt the session tokens in Auth.js. You can generate the key using the following command: `openssl rand -base64 32`.
- Default: `-`
- Example: `Tfhi2t2pelSMEA8eaV61KaqPNEndFFdMIxDaJnS1CUI=`

#### `NEXTAUTH_URL`

- Type: Optional
- Description: This URL is used to specify the callback address for Auth.js during OAuth authentication. It does not need to be set when deploying on Vercel.
- Default: `-`
- Example: `https://example.com/api/auth`

### Auth0

<Callout>
  Currently, we only support the Auth0 identity verification service provider. If you need to use
  other identity verification service providers, you can submit a [feature
  request](https://github.com/lobehub/lobe-chat/issues/new/choose) or Pull Request.
</Callout>

#### `AUTH0_CLIENT_ID`

- Type: Required
- Description: Client ID of the Auth0 application. You can access it [here][auth0-client-page] and navigate to the application settings to view.
- Default: `-`
- Example: `evCnOJP1UX8FMnXR9Xkj5t0NyFn5p70P`

#### `AUTH0_CLIENT_SECRET`

- Type: Required
- Description: Client Secret of the Auth0 application.
- Default: `-`
- Example: `wnX7UbZg85ZUzF6ioxPLnJVEQa1Elbs7aqBUSF16xleBS5AdkVfASS49-fQIC8Rm`

#### `AUTH0_ISSUER`

- Type: Required
- Description: Issuer/domain of the Auth0 application.
- Default: `-`
- Example: `https://example.auth0.com`

## Plugin Service

### `PLUGINS_INDEX_URL`

- Type: Optional
- Description: Index address of the LobeChat plugin market. If you have deployed the plugin market service on your own, you can use this variable to override the default plugin market address.
- Default: `https://chat-plugins.lobehub.com`

### `PLUGIN_SETTINGS`

- Type: Optional
- Description: Used to configure plugin settings. Use the format `plugin-name:setting-field=setting-value` to configure the settings of the plugin. Separate multiple setting fields with a semicolon `;`, and separate multiple plugin settings with a comma `,`.
- Default: `-`
- Example: `search-engine:SERPAPI_API_KEY=xxxxx,plugin-2:key1=value1;key2=value2`

The above example sets the `SERPAPI_API_KEY` of the `search-engine` plugin to `xxxxx`, and sets `key1` of `plugin-2` to `value1`, and `key2` to `value2`. The generated plugin settings configuration is as follows:

```json
{
  "plugin-2": {
    "key1": "value1",
    "key2": "value2"
  },
  "search-engine": {
    "SERPAPI_API_KEY": "xxxxx"
  }
}
```

## Assistant Market

### `AGENTS_INDEX_URL`

- Type: Optional
- Description: Index address of the LobeChat assistant market. If you have deployed the assistant market service on your own, you can use this variable to override the default market address.
- Default: `https://chat-agents.lobehub.com`

## Data Statistics

### Vercel Analytics

#### `NEXT_PUBLIC_ANALYTICS_VERCEL`

- Type: Optional
- Description: Used to configure the environment variable for Vercel Analytics. Set to `1` to enable Vercel Analytics.
- Default: `-`
- Example: `1`

#### `NEXT_PUBLIC_VERCEL_DEBUG`

- Type: Optional
- Description: Used to enable the debug mode for Vercel Analytics.
- Default: `-`
- Example: `1`

### Posthog Analytics

#### `NEXT_PUBLIC_ANALYTICS_POSTHOG`

- Type: Optional
- Description: Used to enable the environment variable for [PostHog Analytics][posthog-analytics-url]. Set to `1` to enable PostHog Analytics.
- Default: `-`
- Example: `1`

#### `NEXT_PUBLIC_POSTHOG_KEY`

- Type: Optional
- Description: Set the PostHog project Key.
- Default: `-`
- Example: `phc_xxxxxxxx`

#### `NEXT_PUBLIC_POSTHOG_HOST`

- Type: Optional
- Description: Set the deployment address of the PostHog service, defaulting to the official SAAS address.
- Default: `https://app.posthog.com`
- Example: `https://example.com`

#### `NEXT_PUBLIC_POSTHOG_DEBUG`

- Type: Optional
- Description: Enable the debug mode for PostHog.
- Default: `-`
- Example: `1`

### Umami Analytics

#### `NEXT_PUBLIC_ANALYTICS_UMAMI`

- Type: Optional
- Description: Used to enable the environment variable for [Umami Analytics][umami-analytics-url]. Set to `1` to enable Umami Analytics.
- Default: `-`
- Example: `1`

#### `NEXT_PUBLIC_UMAMI_SCRIPT_URL`

- Type: Optional
- Description: The URL of the Umami script, defaulting to the script URL provided by Umami Cloud.
- Default: `https://analytics.umami.is/script.js`
- Example: `https://umami.your-site.com/script.js`

#### `NEXT_PUBLIC_UMAMI_WEBSITE_ID`

- Type: Required
- Description: Your Umami Website ID.
- Default: `-`
- Example: `E738D82A-EE9E-4806-A81F-0CA3CAE57F65`

[auth0-client-page]: https://manage.auth0.com/dashboard
[azure-api-verion-url]: https://docs.microsoft.com/zh-cn/azure/developer/javascript/api-reference/es-modules/azure-sdk/ai-translation/translationconfiguration?view=azure-node-latest#api-version
[openai-api-page]: https://platform.openai.com/account/api-keys
[posthog-analytics-url]: https://posthog.com
[umami-analytics-url]: https://umami.is
