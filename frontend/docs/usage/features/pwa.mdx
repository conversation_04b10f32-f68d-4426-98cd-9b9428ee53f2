import { Steps } from 'nextra/components';

# Progressive Web App (PWA)

<Image
  alt={'Progressive Web App (PWA)'}
  src={'https://github.com/lobehub/lobe-chat/assets/28616219/ecc8364a-cfc4-4a3d-b6fd-68b2d9bc5f0d'}
  cover
/>

We understand the importance of providing a seamless experience for users in today's multi-device environment. To achieve this, we have adopted Progressive Web App [PWA](https://support.google.com/chrome/answer/9658361) technology, which is a modern web technology that elevates web applications to a near-native app experience. Through PWA, LobeChat is able to provide a highly optimized user experience on both desktop and mobile devices, while maintaining lightweight and high performance characteristics. Visually and perceptually, we have also carefully designed it to ensure that its interface is indistinguishable from a native app, providing smooth animations, responsive layouts, and adaptation to different screen resolutions of various devices.

If you are unfamiliar with the installation process of PWA, you can follow the steps below to add LobeChat as a desktop app (also applicable to mobile devices):

<Steps>

### Run Chrome or Edge browser on your computer

### Visit the LobeChat webpage

### In the top right corner of the address bar, click the <kbd>Install</kbd> icon

### Follow the on-screen instructions to complete the PWA installation

</Steps>
