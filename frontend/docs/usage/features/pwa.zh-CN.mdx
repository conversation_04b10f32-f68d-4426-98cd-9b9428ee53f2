import { Steps } from 'nextra/components';

# 渐进式 Web 应用（PWA）

<Image
  alt={'渐进式 Web 应用（PWA）'}
  src={'https://github.com/lobehub/lobe-chat/assets/28616219/ecc8364a-cfc4-4a3d-b6fd-68b2d9bc5f0d'}
  cover
/>

我们利深知在当今多设备环境下为用户提供无缝体验的重要性。为此，我们采用了渐进式 Web 应用 [PWA](https://support.google.com/chrome/answer/9658361) 技术，这是一种能够将网页应用提升至接近原生应用体验的现代 Web 技术。通过 PWA，LobeChat 能够在桌面和移动设备上提供高度优化的用户体验，同时保持轻量级和高性能的特点。在视觉和感觉上，我们也经过精心设计，以确保它的界面与原生应用无差别，提供流畅的动画、响应式布局和适配不同设备的屏幕分辨率。

若您未熟悉 PWA 的安装过程，您可以按照以下步骤将 LobeChat 添加为您的桌面应用（也适用于移动设备）：

<Steps>

### 在电脑上运行 Chrome 或 Edge 浏览器

### 访问 LobeChat 网页

### 在地址栏的右上角，单击 <kbd>安装</kbd> 图标

### 根据屏幕上的指示完成 PWA 的安装

</Steps>
