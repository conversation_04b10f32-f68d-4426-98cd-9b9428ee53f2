import { Callout } from 'nextra/components';

# Custom Themes

<Image
  alt={'Custom Themes'}
  src={'https://github.com/lobehub/lobe-chat/assets/28616219/9eca103c-9335-4a4c-8192-271a0b857b26'}
  cover
/>

LobeChat places a strong emphasis on personalized user experiences in its interface design, and thus introduces flexible and diverse theme modes, including a light mode for daytime and a dark mode for nighttime.

In addition to theme mode switching, we also provide a series of color customization options, allowing users to adjust the application's theme colors according to their preferences. Whether it's a stable deep blue, a lively peach pink, or a professional gray and white, users can find color choices in LobeChat that match their own style.

<Callout type={'info'}>
  The default configuration can intelligently identify the user's system color mode and
  automatically switch themes to ensure a consistent visual experience with the operating system.
</Callout>

For users who prefer to manually adjust details, LobeChat also provides intuitive setting options and offers a choice between conversation bubble mode and document mode for chat scenes.
