import { Callout } from 'nextra/components';

# 支持本地大语言模型（LLM）

<Image
  alt={'Ollama 支持本地大语言模型'}
  src={'https://github.com/lobehub/lobe-chat/assets/28616219/ca9a21bc-ea6c-4c90-bf4a-fa53b4fb2b5c'}
  cover
/>

<Callout>在 >=v0.127.0 版本中可用，目前仅支持 Docker 部署</Callout>

随着 LobeChat v0.127.0 的发布，我们迎来了一个激动人心的特性 —— Ollama AI 支持！🤯 在 [Ollama AI](https://ollama.ai/) 强大的基础设施和 [社区的共同努力](https://github.com/lobehub/lobe-chat/pull/1265) 下，现在您可以在 LobeChat 中与本地 LLM (Large Language Model) 进行交流了！🤩

我们非常高兴能在这个特别的时刻，向所有 LobeChat 用户介绍这项革命性的特性。Ollama AI 的集成不仅标志着我们技术上的一个巨大飞跃，更是向用户承诺，我们将不断追求更高效、更智能的沟通方式。

### 如何启动与本地 LLM 的对话？

启动过程异常简单！您只需运行以下 Docker 命令行，就可以在 LobeChat 中体验与本地 LLM 的对话了：

```bash
docker run -d -p 3210:3210 -e OLLAMA_PROXY_URL=http://host.docker.internal:11434/v1 lobehub/lobe-chat
```

是的，就是这么简单！🤩 您不需要进行繁杂的配置，也不必担心复杂的安装过程。我们已经为您准备好了一切，只需一行命令，即可开启与本地 AI 的深度对话。

### 体验前所未有的交互速度

借助 Ollama AI 的强大能力，LobeChat 在进行自然语言处理方面的效率得到了极大的提升。无论是处理速度还是响应时间，都达到了新的高度。这意味着您的对话体验将更加流畅，无需等待，即时得到回应。

### 为什么选择本地 LLM？

与基于云的解决方案相比，本地 LLM 提供了更高的隐私性和安全性。您的所有对话都在本地处理，不经过任何外部服务器，确保了您的数据安全性。此外，本地处理还能减少网络延迟，为您带来更加即时的交流体验。

### 开启您的 LobeChat & Ollama AI 之旅

现在，就让我们一起开启这段激动人心的旅程吧！通过 LobeChat 与 Ollama AI 的协作，探索 AI 带来的无限可能。无论您是技术爱好者，还是对 AI 交流充满好奇，LobeChat 都将为您提供一场前所未有的体验。
