import { Callout } from 'nextra/components';

# More Features

In addition to the above features, our design and technical capabilities will provide you with more assurance in usage:

- [x] 💎 **Exquisite UI Design**: Carefully designed interface with elegant appearance and smooth interaction effects, supporting light and dark themes, and adaptable to mobile devices. Supports PWA, providing an experience closer to native applications.
- [x] 🗣️ **Smooth Conversation Experience**: Responsive design brings a smooth conversation experience and supports full Markdown rendering, including code highlighting, LaTex formulas, Mermaid flowcharts, and more.
- [x] 💨 **Fast Deployment**: Use the Vercel platform or our Docker image, simply click the deploy button, and deployment can be completed within 1 minute without complex configuration processes.
- [x] 🔒 **Privacy and Security**: All data is stored locally in the user's browser, ensuring user privacy and security.
- [x] 🌐 **Custom Domain**: If users have their own domain, they can bind it to the platform for quick access to the chat assistant from anywhere.

> ✨ As the product continues to iterate, we will bring more exciting features!

---

<Callout>
  You can find our upcoming [Roadmap][github-project-link] plans in the Projects section.
</Callout>

[github-project-link]: https://github.com/lobehub/lobe-chat/projects
