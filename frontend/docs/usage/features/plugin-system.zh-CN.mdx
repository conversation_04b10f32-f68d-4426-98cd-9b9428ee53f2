import { Callout, Steps } from 'nextra/components';

# 插件系统

<Image
  alt={'插件系统'}
  src={
    'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/268670883-33c43a5c-a512-467e-855c-fa299548cce5.png'
  }
  cover
/>

LobeChat 的插件生态系统是其核心功能的重要扩展，它极大地增强了 LobeChat 助手的实用性和灵活性。

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/f29475a3-f346-4196-a435-41a6373ab9e2" />

通过利用插件，LobeChat 的助手们能够实现实时信息的获取和处理，例如搜索网络信息，为用户提供即时且相关的资讯。

此外，这些插件不仅局限于新闻聚合，还可以扩展到其他实用的功能，如快速检索文档、生成图片、获取 Bilibili 、Steam 等各种平台数据，以及与其他各式各样的第三方服务交互。

通过查看 [插件使用](/zh/usage/plugins/basic) 了解更多。

<Callout type={'default'}>
  为了帮助开发者更好地参与到这个生态中来，我们在提供了全面的开发资源。这包括详尽的组件开发文档、功能齐全的软件开发工具包（SDK），以及样板示例，这些都是为了简化开发过程，降低开发者的入门门槛。
</Callout>

<Callout type={'info'}>
  我们欢迎开发者利用这些资源，发挥创造力，编写出功能丰富、用户友好的插件。通过共同的努力，我们可以不断扩展聊天应用的功能界限，探索一个更加智能、高效的创造力平台。
</Callout>

📊 Total plugins: [<kbd>**61**</kbd>](https://github.com/lobehub/lobe-chat-plugins)

| 最近新增 | 插件描述 |
| --- | --- |
| [图表和图示](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **pyxl** on **2024-02-05**</sup> | 美人鱼图表，演示文稿方案，分析，研究网站，饼图。<br/>`图表` `图示` |
| [社交搜索](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **say-apps** on **2024-02-02**</sup> | 社交搜索提供访问推文、用户、关注者、图片、媒体等功能。<br/>`社交` `推特` `x` `搜索` |
| [TokenInsights](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **feednews** on **2024-01-27**</sup> | 获取实时加密货币价格，BTC，ETH，BNB 和最新见解。最新的币新闻和空投机会。<br/>`加密货币` `btc` `eth` `bnb` |
| [哔哩哔哩](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **LobeHub** on **2024-01-27**</sup> | 通过关键词视频搜索、回放访问、互动弹幕、热门视频推荐和热搜洞察等功能，深入体验哔哩哔哩丰富的内容，尽在您的指尖。<br/>`视频` `哔哩哔哩` `搜索` |

## 插件生态体系

<Callout>
  如果你对插件开发感兴趣，请在 Wiki 中查阅我们的 [📘 插件开发指南](/zh/usage/plugins/development)。
</Callout>

- [lobe-chat-plugins][lobe-chat-plugins]：这是 LobeChat 的插件索引。它从该仓库的 index.json 中获取插件列表并显示给用户。
- [chat-plugin-template][chat-plugin-template]: Chat Plugin 插件开发模版，你可以通过项目模版快速新建插件项目。
- [@lobehub/chat-plugin-sdk][chat-plugin-sdk]：LobeChat 插件 SDK 可帮助您创建出色的 Lobe Chat 插件。
- [@lobehub/chat-plugins-gateway][chat-plugins-gateway]：LobeChat 插件网关是一个后端服务，作为 LobeChat 插件的网关。我们使用 Vercel 部署此服务。

[lobe-chat-plugins]: https://github.com/lobehub/lobe-chat-plugins
[chat-plugin-sdk]: https://github.com/lobehub/chat-plugin-sdk
[chat-plugin-template]: https://github.com/lobehub/chat-plugin-template
[chat-plugins-gateway]: https://github.com/lobehub/chat-plugins-gateway

### 路线进展

LobeChat 的插件系统目前已初步进入一个稳定阶段，我们已基本完成大部分插件系统所需的功能，但我们仍然在规划与思考插件能为我们带来的全新可能性。您可以在以下 Issues 中了解更多信息:

<Steps>

### ✅ 插件一期

实现插件与主体分离，将插件拆分为独立仓库维护，并实现插件的动态加载。 [**#73**](https://github.com/lobehub/lobe-chat/issues/73)

### ✅ 插件二期

插件的安全性与使用的稳定性，更加精准地呈现异常状态，插件架构的可维护性与开发者友好。[**#97**](https://github.com/lobehub/lobe-chat/issues/97)

### ✅ 插件三期

更高阶与完善的自定义能力，支持 OpenAPI schema 调用、兼容 ChatGPT 插件、新增 Midjourney 插件。 [**#411**](https://github.com/lobehub/lobe-chat/discussions/#411)

### 💭 插件四期

完善的鉴权、可视化配置插件定义、 Plugin SDK CLI 、 Python 语言研发模板、还有什么想法？欢迎参与讨论： [**#1310**](https://github.com/lobehub/lobe-chat/discussions/#1310)

</Steps>
