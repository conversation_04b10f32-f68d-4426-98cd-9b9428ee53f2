import { Callout, Steps } from 'nextra/components';

# Plugin System

<Image
  alt={'Plugin System'}
  src={
    'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/268670883-33c43a5c-a512-467e-855c-fa299548cce5.png'
  }
  cover
/>

The plugin ecosystem of LobeChat is an important extension of its core functionality, greatly enhancing the practicality and flexibility of the LobeChat assistant.

<Video src="https://github.com/lobehub/lobe-chat/assets/28616219/f29475a3-f346-4196-a435-41a6373ab9e2" />

By utilizing plugins, LobeChat assistants can obtain and process real-time information, such as searching for web information and providing users with instant and relevant news.

In addition, these plugins are not limited to news aggregation, but can also extend to other practical functions, such as quickly searching documents, generating images, obtaining data from various platforms like Bilibili, Steam, and interacting with various third-party services.

Learn more about [plugin usage](/en-US/usage/plugins/basic) by checking it out.

<Callout type={'default'}>
  To help developers better participate in this ecosystem, we provide comprehensive development
  resources. This includes detailed component development documentation, a fully-featured software
  development kit (SDK), and template examples, all aimed at simplifying the development process and
  lowering the entry barrier for developers.
</Callout>

<Callout type={'info'}>
  We welcome developers to utilize these resources, unleash their creativity, and write
  feature-rich, user-friendly plugins. Through collective efforts, we can continuously expand the
  functional boundaries of the chat application and explore a more intelligent and efficient
  creativity platform.
</Callout>

📊 Total plugins: [<kbd>**61**</kbd>](https://github.com/lobehub/lobe-chat-plugins)

| Recently Added | Plugin Description |
| --- | --- |
| [Charts and Graphs](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **pyxl** on **2024-02-05**</sup> | Mermaid charts, presentation schemes, analysis, research websites, pie charts.<br/>`Charts` `Graphs` |
| [Social Search](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **say-apps** on **2024-02-02**</sup> | Social search provides access to tweets, users, followers, images, media, and more.<br/>`Social` `Twitter` `x` `Search` |
| [TokenInsights](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **feednews** on **2024-01-27**</sup> | Get real-time cryptocurrency prices, BTC, ETH, BNB, and the latest insights. The latest coin news and airdrop opportunities.<br/>`Cryptocurrency` `btc` `eth` `bnb` |
| [Bilibili](https://chat-preview.lobehub.com/settings/agent)<br/><sup>By **LobeHub** on **2024-01-27**</sup> | Experience Bilibili's rich content through keyword video search, replay access, interactive barrage, popular video recommendations, and hot search insights, all at your fingertips.<br/>`Video` `Bilibili` `Search` |

## Plugin Ecosystem

<Callout>
  If you are interested in plugin development, please refer to our [📘 Plugin Development
  Guide](/en/usage/plugins/development) in the Wiki.
</Callout>

- [lobe-chat-plugins][lobe-chat-plugins]: This is the plugin index for LobeChat. It retrieves the list of plugins from the index.json of this repository and displays them to the users.
- [chat-plugin-template][chat-plugin-template]: Chat Plugin plugin development template, you can quickly create a new plugin project through the project template.
- [@lobehub/chat-plugin-sdk][chat-plugin-sdk]: The LobeChat plugin SDK can help you create excellent Lobe Chat plugins.
- [@lobehub/chat-plugins-gateway][chat-plugins-gateway]: The LobeChat plugin gateway is a backend service that serves as the gateway for LobeChat plugins. We deploy this service using Vercel.

### Roadmap Progress

The plugin system of LobeChat has now entered a stable stage, and we have basically completed most of the functionality required by the plugin system. However, we are still planning and considering the new possibilities that plugins can bring to us. You can learn more in the following Issues:

<Steps>

### ✅ Phase One of Plugins

Implementing the separation of plugins from the main body, splitting the plugins into independent repositories for maintenance, and implementing dynamic loading of plugins. [**#73**](https://github.com/lobehub/lobe-chat/issues/73)

### ✅ Phase Two of Plugins

The security and stability of plugin usage, more accurate presentation of abnormal states, maintainability and developer-friendliness of the plugin architecture. [**#97**](https://github.com/lobehub/lobe-chat/issues/97)

### ✅ Phase Three of Plugins

Higher-level and improved customization capabilities, support for OpenAPI schema invocation, compatibility with ChatGPT plugins, and the addition of Midjourney plugins. [**#411**](https://github.com/lobehub/lobe-chat/discussions/#411)

### 💭 Phase Four of Plugins

Comprehensive authentication, visual configuration of plugin definitions, Plugin SDK CLI, Python language development template, any other ideas? Join the discussion: [**#1310**](https://github.com/lobehub/lobe-chat/discussions/#1310)

</Steps>

[lobe-chat-plugins]: https://github.com/lobehub/lobe-chat-plugins
[chat-plugin-sdk]: https://github.com/lobehub/chat-plugin-sdk
[chat-plugin-template]: https://github.com/lobehub/chat-plugin-template
[chat-plugins-gateway]: https://github.com/lobehub/chat-plugins-gateway
