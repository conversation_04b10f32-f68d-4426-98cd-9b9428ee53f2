# 模型视觉识别

<Image
  alt={'模型视觉识别'}
  src={
    'https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/284072129-382bdf30-e3d6-4411-b5a0-249710b8ba08.png'
  }
  cover
/>

LobeChat 已经支持 OpenAI 的 [`gpt-4-vision`](https://platform.openai.com/docs/guides/vision) 、Google Gemini Pro vision、智谱 GLM-4 Vision 等具有视觉识别能力的大语言模型，这使得 LobeChat 具备了多模态交互的能力。用户可以轻松上传图片或者拖拽图片到对话框中，助手将能够识别图片内容，并在此基础上进行智能对话，构建更智能、更多元化的聊天场景。

这一特性打开了新的互动方式，使得交流不再局限于文字，而是可以涵盖丰富的视觉元素。无论是日常使用中的图片分享，还是在特定行业内的图像解读，助手都能提供出色的对话体验。
