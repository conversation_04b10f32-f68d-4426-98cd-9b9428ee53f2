import { Callout } from 'nextra/components';

# 自定义主题

<Image
  alt={'自定义主题'}
  src={'https://github.com/lobehub/lobe-chat/assets/28616219/9eca103c-9335-4a4c-8192-271a0b857b26'}
  cover
/>

LobeChat 在界面设计上十分考虑用户的个性化体验，因此引入了灵活多变的主题模式，其中包括日间的亮色模式和夜间的深色模式。

除了主题模式的切换，我们还提供了一系列的颜色定制选项，允许用户根据自己的喜好来调整应用的主题色彩。无论是想要沉稳的深蓝，还是希望活泼的桃粉，或者是专业的灰白，用户都能够在 LobeChat 中找到匹配自己风格的颜色选择。

<Callout type={'info'}>
  默认配置能够智能地识别用户系统的颜色模式，自动进行主题切换，以确保应用界面与操作系统保持一致的视觉体验。
</Callout>

对于喜欢手动调控细节的用户，LobeChat 同样提供了直观的设置选项，针对聊天场景也提供了对话气泡模式和文档模式的选择。
