import { Callout } from 'nextra/components';

# Prompt 使用指南

## Prompt 基本概念

生成式 AI 非常有用，但它需要人类指导。通常情况下，生成式 AI 能就像公司新来的实习生一样，非常有能力，但需要清晰的指示才能做得好。能够正确地指导生成式 AI 是一项非常强大的技能。你可以通过发送一个 prompt 来指导生成式 AI，这通常是一个文本指令。Prompt 是向助手提供的输入，它会影响输出结果。一个好的 Prompt 应该是结构化的，清晰的，简洁的，并且具有指向性。

## 如何写好一个结构化 prompt

<Callout type={'info'}>
  结构化 prompt 是指 prompt 的构造应该有明确的逻辑和结构。例如，如果你想让模型生成一篇文章，你的
  prompt 可能需要包括文章的主题，文章的大纲，文章的风格等信息。
</Callout>

让我们看一个基本的讨论问题的例子:

> _"我们星球面临的最紧迫的环境问题是什么，个人可以采取哪些措施来帮助解决这些问题？"_

我们可以将其转化为简单的助手提示，将回答以下问题：放在前面。

```prompt
回答以下问题:
我们星球面临的最紧迫的环境问题是什么，个人可以采取哪些措施来帮助解决这些问题？
```

由于这个提示生成的结果并不一致，有些只有一两个句子。一个典型的讨论回答应该有多个段落，因此这些结果并不理想。一个好的提示应该给出**具体的格式和内容指令**。您需要消除语言中的歧义以提高一致性和质量。这是一个更好的提示。

```prompt
写一篇高度详细的论文，包括引言、正文和结论段，回答以下问题:
我们星球面临的最紧迫的环境问题是什么，
个人可以采取哪些措施来帮助解决这些问题？
```

第二个提示生成了更长的输出和更好的结构。提示中使用 “论文” 一词是有意的，因为助手可以理解论文的定义，因此更有可能生成连贯的、结构化的回答。

<br />

## 如何提升其质量和效果

<Callout type={'info'}>
提升 prompt 质量和效果的方法主要有以下几点：

- **尽量明确你的需求：** 模型的输出会尽可能满足你的需求，所以如果你的需求不明确，输出可能会不如预期。
- **使用正确的语法和拼写：** 模型会尽可能模仿你的语言风格，所以如果你的语言风格有问题，输出可能也会有问题。
- **提供足够的上下文信息：** 模型会根据你提供的上下文信息生成输出，所以如果你提供的上下文信息不足，可能无法生成你想要的结果。

</Callout>

在为讨论问题制定有效的提示后，您现在需要细化生成的结果。这可能涉及到调整输出以符合诸如字数等限制，或将不同生成的结果的概念组合在一起。

迭代的一个简单方法是生成多个输出并查看它们，以了解正在使用的概念和结构。一旦评估了输出，您就可以选择最合适的输出并将它们组合成一个连贯的回答。另一种迭代的方法是逐步开始，然后**逐步扩展**。这需要不止一个提示：一个起始提示，用于撰写最初的一两段，然后是其他提示，以扩展已经写过的内容。以下是一个潜在的哲学讨论问题:

> _"数学是发明还是发现？用仔细的推理来解释你的答案。"_

将其添加到一个简单的提示中，如下所示:

```prompt
写一个起始段回答以下问题:
数学是发明还是发现？用仔细的推理来解释你的答案。
```

生成了一些回答，找到了一个我们喜欢的：

```prompt
哲学家和数学家之间争论不休，数学是发明还是发现。
一些人认为，数学的概念和原则独立于人类思维存在，因此可以被认为是被发现而不是被发明的。
例如，数字、比率和几何形状的概念在自然界中被观察到，可以被抽象化和系统化地研究。
```

是一个不错的开端。然后使用另一个提示来扩展：

```prompt
我正在撰写一篇详细的短文，回答以下提示:
数学是发明还是发现？用仔细的推理来解释你的答案。这是我目前的情况: 哲学家和数学家之间争论不休，数学是发明还是发现。
一些人认为，数学的概念和原则独立于人类思维存在，因此可以被认为是被发现而不是被发明的。
例如，数字、比率和几何形状的概念在自然界中被观察到，可以被抽象化和系统化地研究。写下我文章的下一段。
```

使用此提示生成了几个扩展，并选择了一个我喜欢的，最终得到了以下结果：

```prompt
哲学家和数学家之间争论不休，数学是发明还是发现。
一些人认为，数学的概念和原则独立于人类思维存在，因此可以被认为是被发现而不是被发明的。
例如，数字、比率和几何形状的概念在自然界中被观察到，可以被抽象化和系统化地研究。
另一方面，有人认为数学在很大程度上是人类思维的发明。
数学原理基于逻辑推理，这是人类思维的一个特征。
例如，欧几里德几何基于抽象的公理和前提，被接受为真实而无需证明。
因此，可以认为几何是人类思维的发明，而不是一种发现。
同样，数学公式和方程用于模拟和预测物理现象，这又是人类推理的结果。
```

使用扩展提示，我们可以逐步地写作并在每个步骤上进行迭代。这对于需要**生成更高质量的输出并希望逐步修改**的情况非常有用。

## 扩展阅读

- **Learn Prompting**: https://learnprompting.org/zh-Hans/docs/intro
