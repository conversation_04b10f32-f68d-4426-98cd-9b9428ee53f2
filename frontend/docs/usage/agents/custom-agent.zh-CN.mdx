import { Callout, Cards } from 'nextra/components';

# 自定义助手指南

作为 LobeChat 的基础职能单位，助手的添加和迭代是非常重要的。现在你可以通过两种方式将助手添加到你的常用列表中

## `A` 通过角色市场添加

如果你是一个 Prompt 编写的新手，不妨先浏览一下 LobeChat 的助手市场。在这里，你可以找到其他人提交的常用助手，并且只需一键添加到你的列表中，非常方便。

![](https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279588466-4c32041b-a8e6-4703-ba4a-f91b7800e359.png)

## `B` 通过新建自定义助手

当你需要处理一些特定的任务时，你就需要考虑创建一个自定义助手来帮助你解决问题。可以通过以下方式添加并进行助手的详细配置

![](https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587283-a3ea8dfd-70fb-47ee-ab00-e3911ac6a939.png) ![](https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587292-a3d102c6-f61e-4578-91f1-c0a4c97588e1.png)

<Callout type={'info'}>
  **快捷设置技巧**: 可以通过侧边栏的快捷编辑按钮进行 Prompt 的便捷修改

</Callout>

![](https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587294-388d1877-193e-4a50-9fe8-8fbcc3ccefa0.png) ![](https://github-production-user-asset-6210df.s3.amazonaws.com/17870709/279587298-333da153-13b8-4557-a0a2-cff55e7bc1c0.png)

如果你希望理解 Prompt 编写技巧和常见的模型参数设置，可以继续查看：

<Cards>
  <Cards.Card href={'/zh/usage/agents/prompt'} title={'Prompt 使用指南'}></Cards.Card>
  <Cards.Card href={'/zh/usage/agents/model'} title={'大语言模型使用指南'}></Cards.Card>
</Cards>
