# New Locale Guide

LobeChat uses [lobe-i18n](https://github.com/lobehub/lobe-cli-toolbox/tree/master/packages/lobe-i18n) as the i18n solution, which allows for quick addition of new language support in the application.

## TOC

- [Adding New Language Support](#adding-new-language-support)
  - [Step 1: Update the Internationalization Configuration File](#step-1-update-the-internationalization-configuration-file)
  - [Step 2: Automatically Translate Language Files](#step-2-automatically-translate-language-files)
  - [Step 3: Submit and Review Your Changes](#step-3-submit-and-review-your-changes)
  - [Additional Information](#additional-information)

## Adding New Language Support

To add new language internationalization support in LobeChat (for example, adding Vietnamese `vi-VN`), please follow the steps below:

### Step 1: Update the Internationalization Configuration File

1. Open the `.i18nrc.js` file. You can find this file in the project's root directory.
2. Add the new language code to the configuration file. For example, to add Vietnamese, you need to add `'vi-VN'` to the configuration file.

```js
module.exports = {
  // ... Other configurations

  outputLocales: [
    'zh-TW',
    'en-US',
    'ru-RU',
    'ja-JP',
    // ...Other languages

    'vi-VN', // Add 'vi-VN' to the array
  ],
};
```

### Step 2: Automatically Translate Language Files

LobeChat uses the `lobe-i18n` tool to automatically translate language files, so manual updating of i18n files is not required.

Run the following command to automatically translate and generate the Vietnamese language files:

```bash
npm run i18n
```

This will utilize the `lobe-i18n` tool to process the language files.

### Step 3: Submit and Review Your Changes

Once you have completed the above steps, you need to submit your changes and create a Pull Request.

Ensure that you follow LobeChat's contribution guidelines and provide a necessary description to explain your changes. For example, refer to a similar previous Pull Request [#759](https://github.com/lobehub/lobe-chat/pull/759).

### Additional Information

- After submitting your Pull Request, please patiently wait for the project maintainers to review it.
- If you encounter any issues, you can reach out to the LobeChat community for assistance.
- For more accurate results, ensure that your Pull Request is based on the latest main branch and stays in sync with the main branch.

By following the above steps, you can successfully add new language support to LobeChat and ensure that the application provides a localized experience for more users.
