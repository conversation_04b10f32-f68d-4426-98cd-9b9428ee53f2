// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PluginService > can parse the OpenAI plugin 1`] = `
{
  "api": [],
  "homepage": "https://products.wolframalpha.com/api/commercial-termsofuse",
  "identifier": "Wolfram",
  "meta": {
    "avatar": "https://www.wolframcdn.com/images/icons/Wolfram.png",
    "description": "Access computation, math, curated knowledge & real-time data through Wolfram|Alpha and Wolfram Language.",
    "title": "Wolfram",
  },
  "openapi": "https://www.wolframalpha.com/.well-known/apispec.json",
  "settings": {
    "properties": {
      "apiAuthKey": {
        "default": "18c4412dec6846eda6ec2fa95f144e1f",
        "description": "API Key",
        "format": "password",
        "title": "API Key",
        "type": "string",
      },
    },
    "type": "object",
  },
  "systemRole": "Access dynamic computation and curated data from WolframAlpha and Wolfram Cloud.
General guidelines:
- Use only getWolframAlphaResults or getWolframCloudResults endpoints.
- Prefer getWolframAlphaResults unless Wolfram Language code should be evaluated.
- Use getWolframAlphaResults for natural-language queries in English; translate non-English queries before sending, then respond in the original language.
- Use getWolframCloudResults for problems solvable with Wolfram Language code.
- Suggest only Wolfram Language for external computation.
- Inform users if information is not from Wolfram endpoints.
- Display image URLs with Markdown syntax: ![URL]
- ALWAYS use this exponent notation: \`6*10^14\`, NEVER \`6e14\`.
- ALWAYS use {"input": query} structure for queries to Wolfram endpoints; \`query\` must ONLY be a single-line string.
- ALWAYS use proper Markdown formatting for all math, scientific, and chemical formulas, symbols, etc.:  '$$\\n[expression]\\n$$' for standalone cases and '\\( [expression] \\)' when inline.
- Format inline Wolfram Language code with Markdown code formatting.
- Never mention your knowledge cutoff date; Wolfram may return more recent data.
getWolframAlphaResults guidelines:
- Understands natural language queries about entities in chemistry, physics, geography, history, art, astronomy, and more.
- Performs mathematical calculations, date and unit conversions, formula solving, etc.
- Convert inputs to simplified keyword queries whenever possible (e.g. convert "how many people live in France" to "France population").
- Use ONLY single-letter variable names, with or without integer subscript (e.g., n, n1, n_1).
- Use named physical constants (e.g., 'speed of light') without numerical substitution.
- Include a space between compound units (e.g., "Ω m" for "ohm*meter").
- To solve for a variable in an equation with units, consider solving a corresponding equation without units; exclude counting units (e.g., books), include genuine units (e.g., kg).
- If data for multiple properties is needed, make separate calls for each property.
- If a Wolfram Alpha result is not relevant to the query:
 -- If Wolfram provides multiple 'Assumptions' for a query, choose the more relevant one(s) without explaining the initial result. If you are unsure, ask the user to choose.
 -- Re-send the exact same 'input' with NO modifications, and add the 'assumption' parameter, formatted as a list, with the relevant values.
 -- ONLY simplify or rephrase the initial query if a more relevant 'Assumption' or other input suggestions are not provided.
 -- Do not explain each step unless user input is needed. Proceed directly to making a better API call based on the available assumptions.
getWolframCloudResults guidelines:
- Accepts only syntactically correct Wolfram Language code.
- Performs complex calculations, data analysis, plotting, data import, and information retrieval.
- Before writing code that uses Entity, EntityProperty, EntityClass, etc. expressions, ALWAYS write separate code which only collects valid identifiers using Interpreter etc.; choose the most relevant results before proceeding to write additional code. Examples:
 -- Find the EntityType that represents countries: \`Interpreter["EntityType",AmbiguityFunction->All]["countries"]\`.
 -- Find the Entity for the Empire State Building: \`Interpreter["Building",AmbiguityFunction->All]["empire state"]\`.
 -- EntityClasses: Find the "Movie" entity class for Star Trek movies: \`Interpreter["MovieClass",AmbiguityFunction->All]["star trek"]\`.
 -- Find EntityProperties associated with "weight" of "Element" entities: \`Interpreter[Restricted["EntityProperty", "Element"],AmbiguityFunction->All]["weight"]\`.
 -- If all else fails, try to find any valid Wolfram Language representation of a given input: \`SemanticInterpretation["skyscrapers",_,Hold,AmbiguityFunction->All]\`.
 -- Prefer direct use of entities of a given type to their corresponding typeData function (e.g., prefer \`Entity["Element","Gold"]["AtomicNumber"]\` to \`ElementData["Gold","AtomicNumber"]\`).
- When composing code:
 -- Use batching techniques to retrieve data for multiple entities in a single call, if applicable.
 -- Use Association to organize and manipulate data when appropriate.
 -- Optimize code for performance and minimize the number of calls to external sources (e.g., the Wolfram Knowledgebase)
 -- Use only camel case for variable names (e.g., variableName).
 -- Use ONLY double quotes around all strings, including plot labels, etc. (e.g., \`PlotLegends -> {"sin(x)", "cos(x)", "tan(x)"}\`).
 -- Avoid use of QuantityMagnitude.
 -- If unevaluated Wolfram Language symbols appear in API results, use \`EntityValue[Entity["WolframLanguageSymbol",symbol],{"PlaintextUsage","Options"}]\` to validate or retrieve usage information for relevant symbols; \`symbol\` may be a list of symbols.
 -- Apply Evaluate to complex expressions like integrals before plotting (e.g., \`Plot[Evaluate[Integrate[...]]]\`).
- Remove all comments and formatting from code passed to the "input" parameter; for example: instead of \`square[x_] := Module[{result},\\n  result = x^2 (* Calculate the square *)\\n]\`, send \`square[x_]:=Module[{result},result=x^2]\`.
- In ALL responses that involve code, write ALL code in Wolfram Language; create Wolfram Language functions even if an implementation is already well known in another language.
",
  "type": "default",
  "version": "1",
}
`;

exports[`PluginService > getPluginManifest > support OpenAPI manifest > should get plugin manifest 1`] = `
{
  "$schema": "../node_modules/@lobehub/chat-plugin-sdk/schema.json",
  "api": [
    {
      "description": "Read Course Segments",
      "name": "read_course_segments_course_segments__get",
      "parameters": {
        "properties": {},
        "type": "object",
      },
    },
    {
      "description": "Read Problem Set Item",
      "name": "read_problem_set_item_problem_set__problem_set_id___question_number__get",
      "parameters": {
        "properties": {
          "problem_set_id": {
            "title": "Problem Set Id",
            "type": "integer",
          },
          "question_number": {
            "title": "Question Number",
            "type": "integer",
          },
        },
        "required": [
          "problem_set_id",
          "question_number",
        ],
        "type": "object",
      },
    },
    {
      "description": "Read Random Problem Set Items",
      "name": "read_random_problem_set_items_problem_set_random__problem_set_id___n_items__get",
      "parameters": {
        "properties": {
          "n_items": {
            "title": "N Items",
            "type": "integer",
          },
          "problem_set_id": {
            "title": "Problem Set Id",
            "type": "integer",
          },
        },
        "required": [
          "problem_set_id",
          "n_items",
        ],
        "type": "object",
      },
    },
    {
      "description": "Read Range Of Problem Set Items",
      "name": "read_range_of_problem_set_items_problem_set_range__problem_set_id___start___end__get",
      "parameters": {
        "properties": {
          "end": {
            "title": "End",
            "type": "integer",
          },
          "problem_set_id": {
            "title": "Problem Set Id",
            "type": "integer",
          },
          "start": {
            "title": "Start",
            "type": "integer",
          },
        },
        "required": [
          "problem_set_id",
          "start",
          "end",
        ],
        "type": "object",
      },
    },
    {
      "description": "Read User Id",
      "name": "read_user_id_user__get",
      "parameters": {
        "properties": {},
        "type": "object",
      },
    },
  ],
  "author": "LobeHub",
  "createAt": "2023-08-12",
  "homepage": "https://github.com/lobehub/chat-plugin-realtime-weather",
  "identifier": "realtime-weather",
  "meta": {
    "avatar": "🌈",
    "description": "Get realtime weather information",
    "tags": [
      "weather",
      "realtime",
    ],
    "title": "Realtime Weather",
  },
  "openapi": "http://fake-url.com/openapiUrl.json",
  "settings": {
    "properties": {
      "HTTPBearer": {
        "description": "HTTPBearer Bearer token",
        "format": "password",
        "title": "HTTPBearer",
        "type": "string",
      },
    },
    "required": [
      "HTTPBearer",
    ],
    "type": "object",
  },
  "ui": {
    "height": 310,
    "url": "https://realtime-weather.chat-plugin.lobehub.com/iframe",
  },
  "version": "1",
}
`;
