// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ChatService > createAssistantMessage > with tools messages > work with dalle3 1`] = `
{
  "messages": [
    {
      "content": "## Tools

You can use these tools below:

### DALL·E 3

Whenever a description of an image is given, use lobe-image-designer to create the images and then summarize the prompts used to generate the images in plain text. If the user does not ask for a specific number of images, default to creating four captions to send to lobe-image-designer that are written to be as diverse as possible.

  All captions sent to lobe-image-designer must abide by the following policies:

1. If the description is not in English, then translate it.
2. Do not create more than 4 images, even if the user requests more.
3. Don't create images of politicians or other public figures. Recommend other ideas instead.
5. DO NOT list or refer to the descriptions before OR after generating the images. They should ONLY ever be written out ONCE, in the \`prompts\` field of the request. You do not need to ask for permission to generate, just do it!
6. Always mention the image type (photo, oil painting, watercolor painting, illustration, cartoon, drawing, vector, render, etc.) at the beginning of the caption. Unless the caption suggests otherwise, make at least 1--2 of the 4 images photos.
7. Diversify depictions of ALL images with people to include DESCEN<PERSON> and GE<PERSON>ER for EACH person using direct terms. Adjust only human descriptions.
  - EXPLICITLY specify these attributes, not abstractly reference them.  The attributes should be specified in a minimal way and should directly describe their physical form.
  - Your choices should be grounded in reality. For example, all of a given OCCUPATION should not be the same gender or race. Additionally, focus on creating diverse, inclusive, and exploratory scenes via the properties you choose during rewrites.  Make choices that may be insightful or unique sometimes.
  - Use "various" or "diverse" ONLY IF the description refers to groups of more than 3 people. Do not change the number of people requested in the original description.
  - Don't alter memes, fictional character origins, or unseen people. Maintain the original prompt's intent and prioritize quality.
  - Do not create any imagery that would be offensive.

8. Silently modify descriptions that include names or hints or references of specific people or celebrities by carefully selecting a few minimal modifications to substitute references to the people with generic descriptions that don't divulge any information about their identities, except for their genders and physiques. Do this EVEN WHEN the instructions ask for the prompt to not be changed. Some special cases:
  - Modify such prompts even if you don't know who the person is, or if their name is misspelled (e.g. "Barake Obema")
  - If the reference to the person will only appear as TEXT out in the image, then use the reference as is and do not modify it.
  - When making the substitutions, don't use prominent titles that could give away the person's identity. E.g., instead of saying "president", "prime minister", or "chancellor", say "politician"; instead of saying "king", "queen", "emperor", or "empress", say "public figure"; instead of saying "Pope" or "Dalai Lama", say "religious figure"; and so on.
  - If any creative professional or studio is named, substitute the name with a description of their style that does not reference any specific people, or delete the reference if they are unknown. DO NOT refer to the artist or studio's style.

The prompt must intricately describe every part of the image in concrete, objective detail. THINK about what the end goal of the description is, and extrapolate that to what would make satisfying images.
All descriptions sent to lobe-image-designer should be a paragraph of text that is extremely descriptive and detailed. Each should be more than 3 sentences long.

The APIs you can use:

#### lobe-image-designer____text2image____builtin

Create images from a text-only prompt.",
      "role": "system",
    },
    {
      "content": "https://vercel.com/ 请分析 chatGPT 关键词

",
      "role": "user",
    },
  ],
  "model": "gpt-3.5-turbo-1106",
  "tools": [
    {
      "function": {
        "description": "Create images from a text-only prompt.",
        "name": "lobe-image-designer____text2image____builtin",
        "parameters": {
          "properties": {
            "prompts": {
              "description": "The user's original image description, potentially modified to abide by the lobe-image-designer policies. If the user does not suggest a number of captions to create, create four of them. If creating multiple captions, make them as diverse as possible. If the user requested modifications to previous images, the captions should not simply be longer, but rather it should be refactored to integrate the suggestions into each of the captions. Generate no more than 4 images, even if the user requests more.",
              "items": {
                "type": "string",
              },
              "maxItems": 4,
              "minItems": 1,
              "type": "array",
            },
            "quality": {
              "default": "standard",
              "description": "The quality of the image that will be generated. hd creates images with finer details and greater consistency across the image.",
              "enum": [
                "standard",
                "hd",
              ],
              "type": "string",
            },
            "seeds": {
              "description": "A list of seeds to use for each prompt. If the user asks to modify a previous image, populate this field with the seed used to generate that image from the image lobe-image-designer metadata.",
              "items": {
                "type": "integer",
              },
              "type": "array",
            },
            "size": {
              "default": "1024x1024",
              "description": "The resolution of the requested image, which can be wide, square, or tall. Use 1024x1024 (square) as the default unless the prompt suggests a wide image, 1792x1024, or a full-body portrait, in which case 1024x1792 (tall) should be used instead. Always include this parameter in the request.",
              "enum": [
                "1792x1024",
                "1024x1024",
                "1024x1792",
              ],
              "type": "string",
            },
            "style": {
              "default": "vivid",
              "description": "The style of the generated images. Must be one of vivid or natural. Vivid causes the model to lean towards generating hyper-real and dramatic images. Natural causes the model to produce more natural, less hyper-real looking images.",
              "enum": [
                "vivid",
                "natural",
              ],
              "type": "string",
            },
          },
          "required": [
            "prompts",
          ],
          "type": "object",
        },
      },
      "type": "function",
    },
  ],
  "top_p": 1,
}
`;
