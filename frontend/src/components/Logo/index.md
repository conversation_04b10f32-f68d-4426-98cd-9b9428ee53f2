---
nav: Components
group: Theme
title: Logo
description: The Logo component is used to display a logo with various types, including 3D, flat, high-contrast, text, and combined. It can also accept an additional React node to be rendered next to the logo.
---

## Default

<code src="./demos/index.tsx" nopadding></code>

## Extra text

<code src="./demos/ExtraText.tsx" nopadding></code>

## APIs

<API></API>
