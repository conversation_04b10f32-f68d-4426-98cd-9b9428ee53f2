# add a access code to lock your lobe-chat application, you can set a long password to avoid leaking. If this value contains a comma, it is a password array.
# ACCESS_CODE=lobe66

# add your custom model name, multi model separate by comma. for example gpt-3.5-1106,gpt-4-1106
# CUSTOM_MODELS=model1,model2,model3

# Specify your API Key selection method, currently supporting `random` and `turn`.
# API_KEY_SELECT_MODE=random

# ---- only choose one from OpenAI Service and Azure OpenAI Service ---- #

########################################
############ OpenAI Service ############
########################################

# you openai api key
OPENAI_API_KEY = sk-xxxxxxxxx

# use a proxy to connect to the OpenAI API
# OPENAI_PROXY_URL=https://api.openai.com/v1

########################################
######### Azure OpenAI Service #########
########################################
# you can learn azure OpenAI Service on https://learn.microsoft.com/en-us/azure/ai-services/openai/overview

# use Azure OpenAI Service by uncomment the following line
# USE_AZURE_OPENAI=1

# The API key you applied for on the Azure OpenAI account page, which can be found in the "Keys and Endpoints" section.
# AZURE_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# The endpoint you applied for on the Azure OpenAI account page, which can be found in the "Keys and Endpoints" section.
# OPENAI_PROXY_URL=https://docs-test-001.openai.azure.com

# Azure's API version, follows the YYYY-MM-DD format
# AZURE_API_VERSION=2023-08-01-preview

########################################
############ ZhiPu AI Service ##########
########################################

# ZHIPU_API_KEY=xxxxxxxxxxxxxxxxxxx.xxxxxxxxxxxxx

########################################
########## Moonshot AI Service #########
########################################

# MOONSHOT_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

########################################
########### Google AI Service ##########
########################################

# GOOGLE_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

########################################
######### AWS Bedrock Service ##########
########################################

# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=xxxxxxxxxxxxxxxxxxx
# AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

########################################
######### Ollama AI Service ##########
########################################

# You can use ollama to get and run LLM locally, learn more about it via https://github.com/ollama/ollama
# The local/remote ollama service url
# OLLAMA_PROXY_URL=http://127.0.0.1:11434/v1

########### Mistral AI Service ##########
########################################

# MISTRAL_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

########################################
######### Perplexity Service ##########
########################################

# PERPLEXITY_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

########################################
######### Anthropic Service ##########
########################################

# ANTHROPIC_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxx

########################################
############ Market Service ############
########################################

# The LobeChat agents market index url
# AGENTS_INDEX_URL=https://chat-agents.lobehub.com

########################################
############ Plugin Service ############
########################################

# you can use ChatChat.The local/remote ChatChat service url
CHATCHAT_PROXY_URL = 'http://localhost:7861/v1'

# Knowledge Base Service. Default as follows
KNOWLEDGE_PROXY_URL = 'http://localhost:7861/knowledge_base'

# The LobeChat plugins store index url
# PLUGINS_INDEX_URL=https://chat-plugins.lobehub.com

# set the plugin settings
# the format is `plugin-identifier:key1=value1;key2=value2`, multiple settings fields are separated by semicolons `;`, multiple plugin settings are separated by commas `,`.
# PLUGIN_SETTINGS=search-engine:SERPAPI_API_KEY=xxxxx
