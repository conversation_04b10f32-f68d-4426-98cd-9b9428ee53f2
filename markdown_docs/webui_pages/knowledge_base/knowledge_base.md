## FunctionDef config_aggrid(df, columns, selection_mode, use_checkbox)
**config_aggrid**: 此函数用于配置AG Grid网格视图的选项，以展示和操作pandas DataFrame数据。

**参数**:
- `df`: pd.DataFrame类型，表示需要在AG Grid中展示的数据。
- `columns`: 字典类型，默认为空字典。用于自定义列的配置，键为列名和表头的元组，值为该列的配置字典。
- `selection_mode`: 字面量类型，可选值为"single"、"multiple"或"disabled"，默认为"single"。用于配置行选择模式。
- `use_checkbox`: 布尔类型，表示是否在行选择时使用复选框。

**代码描述**:
此函数首先基于传入的DataFrame `df` 创建一个GridOptionsBuilder实例。接着，为编号列"No"配置宽度为40。然后，遍历`columns`参数中的每一列配置，应用这些自定义配置到相应的列上。此外，函数还配置了行选择功能，包括选择模式、是否使用复选框以及预选中的行。最后，配置了分页功能，包括启用分页、设置自动分页大小为关闭以及每页显示的行数为10。函数返回配置好的GridOptionsBuilder实例。

在项目中，`config_aggrid`函数被`knowledge_base_page`函数调用，用于在知识库页面中展示知识库文件的详细信息。通过配置AG Grid，用户可以在网页上查看知识库文件的列表，包括文件名、文档加载器、文档数量等信息，并且可以根据配置选择单个或多个文件进行操作。

**注意**:
- 在使用此函数时，确保传入的DataFrame `df` 已正确准备，且`columns`参数中的列名与DataFrame中的列名相匹配。
- 根据实际需求调整行选择模式和是否使用复选框，以适应不同的用户交互场景。

**输出示例**:
由于此函数返回的是一个GridOptionsBuilder实例，而非直接的视觉输出，因此无法提供具体的视觉示例。但可以预期，返回的GridOptionsBuilder实例将被用于AG Grid组件中，生成一个具有分页、自定义列宽和行选择功能的表格视图。
## FunctionDef file_exists(kb, selected_rows)
**file_exists**: 此函数的功能是检查本地知识库文件夹中是否存在指定的文档文件，并返回该文件的名称和路径。

**参数**:
- kb: 字符串类型，表示知识库的名称。
- selected_rows: 列表类型，包含选中的行信息，通常是文档的元数据。

**代码描述**:
`file_exists` 函数接收两个参数：`kb` 和 `selected_rows`。`kb` 参数用于指定知识库的名称，而 `selected_rows` 参数则包含了用户在界面上选中的行信息，通常这些信息中会包含文件的名称。函数首先检查 `selected_rows` 是否非空，如果非空，则从中提取第一行对应的文件名称（`file_name`）。随后，调用 `get_file_path` 函数，传入知识库名称 `kb` 和文件名称 `file_name`，以获取该文件的完整路径。如果该路径对应的文件确实存在（即 `os.path.isfile(file_path)` 返回 `True`），则函数返回文件名称和文件路径。如果 `selected_rows` 为空或文件不存在，则函数返回两个空字符串。

**注意**:
- 在调用此函数之前，应确保 `selected_rows` 参数中包含了正确的文件元数据信息，特别是文件名称。
- 此函数依赖于 `get_file_path` 函数来构造文件的完整路径，因此需要确保 `get_file_path` 函数能够正确执行并返回有效的文件路径。
- 此函数返回的文件路径是基于服务器的文件系统结构的，因此在客户端使用时需要注意路径的有效性和访问权限。

**输出示例**:
假设知识库名称为 `my_knowledge_base`，用户选中的行中包含文件名称 `example.docx`，且该文件确实存在于服务器上的知识库文件夹中，则函数调用 `file_exists('my_knowledge_base', [{'file_name': 'example.docx'}])` 将返回:
```
('example.docx', '/var/knowledge_bases/my_knowledge_base/content/example.docx')
```
这个返回值表示了文件名称和该文件在服务器上的完整路径。如果文件不存在或 `selected_rows` 为空，则函数调用将返回两个空字符串：`("", "")`。
## FunctionDef knowledge_base_page(api, is_lite)
**knowledge_base_page**: 此函数用于在Web UI中展示和管理知识库页面。

**参数**:
- `api`: ApiRequest对象，用于执行与后端API的交互。
- `is_lite`: 布尔类型，可选参数，默认为None，用于指示是否为轻量模式。

**代码描述**:
`knowledge_base_page`函数首先尝试获取知识库的详细信息列表，如果获取过程中发生异常，则显示错误信息并停止执行。接着，根据会话状态中选中的知识库名称，确定当前选中的知识库索引。如果会话状态中没有选中的知识库信息，则默认选中第一个知识库。函数进一步提供了创建新知识库的表单，允许用户输入新知识库的名称、简介、向量库类型和嵌入模型。用户可以上传文件到选中的知识库，并对知识库中的文件进行管理，如添加、删除文件或从向量库中删除文件。此外，还提供了重建向量库、删除知识库等高级功能。用户还可以通过侧边栏进行关键字查询和设置匹配条数。

**注意**:
- 在使用此函数之前，需要确保后端API服务正常运行，以便能够成功获取知识库信息和执行其他操作。
- 创建新知识库时，需要确保知识库名称的唯一性，避免与现有知识库重名。
- 文件上传和知识库管理功能依赖于用户的输入和选择，因此在设计UI时应确保良好的用户体验和输入验证。
- 重建向量库和删除知识库等操作可能会影响知识库的数据完整性和可用性，应谨慎使用。

**输出示例**:
由于`knowledge_base_page`函数主要负责Web UI的展示和交互，其直接的输出是用户界面的变化，而非具体的数据结构。例如，当用户成功创建一个新的知识库后，页面上会显示一个提示信息，如"知识库创建成功"，并且新创建的知识库会出现在知识库列表中。如果用户尝试上传文件到知识库，页面上会显示上传进度，并在完成后显示相应的成功或失败信息。
### FunctionDef format_selected_kb(kb_name)
**format_selected_kb**: 此函数的功能是格式化选定的知识库名称。

**参数**:
- `kb_name`: 字符串类型，代表知识库的名称。

**代码描述**:
`format_selected_kb`函数接受一个参数`kb_name`，这是一个字符串，代表要格式化的知识库名称。函数首先尝试从一个预定义的字典`kb_list`中获取与`kb_name`对应的知识库信息。如果找到了对应的知识库信息，函数将返回一个格式化的字符串，该字符串包含知识库的名称、其版本类型(`vs_type`)以及嵌入模型(`embed_model`)。格式为`"知识库名称 (版本类型 @ 嵌入模型)"`。如果在`kb_list`中没有找到对应的知识库信息，函数将直接返回输入的知识库名称。

**注意**:
- 确保`kb_list`是一个有效的字典，并且已经被正确初始化和填充了相应的知识库信息。
- 输入的知识库名称`kb_name`应该是一个准确且存在于`kb_list`中的键值，以确保能够正确地获取知识库信息。
- 此函数返回的字符串格式特别适用于需要展示知识库详细信息的场景，例如用户界面的下拉菜单或信息展示板。

**输出示例**:
假设`kb_list`字典中有一个条目是`{"example_kb": {"vs_type": "v1", "embed_model": "model123"}}`，并且调用`format_selected_kb("example_kb")`，那么函数将返回字符串`"example_kb (v1 @ model123)"`。如果`kb_name`不在`kb_list`中，比如调用`format_selected_kb("unknown_kb")`，则函数将返回`"unknown_kb"`。
***
