## FunctionDef chat_feedback(message_id, score, reason)
**chat_feedback**: 此函数用于处理用户对聊天记录的反馈。

**参数**:
- `message_id`: 聊天记录的唯一标识ID，用于定位需要反馈的聊天记录。此参数最大长度为32个字符。
- `score`: 用户对聊天记录的评分，满分为100。评分越高表示用户对聊天记录的满意度越高。
- `reason`: 用户提供的评分理由，例如聊天记录不符合事实等。

**代码描述**:
`chat_feedback`函数首先尝试调用`feedback_message_to_db`函数，将用户的反馈信息（包括聊天记录ID、评分和评分理由）存储到数据库中。如果在执行过程中遇到任何异常，函数将捕获这些异常，并通过`logger.error`记录错误信息，同时返回一个包含错误信息的`BaseResponse`对象，状态码为500，表示服务器内部错误。如果没有发生异常，函数将返回一个状态码为200的`BaseResponse`对象，表示用户反馈已成功处理，并附带消息“已反馈聊天记录 {message_id}”。

**注意**:
- 在调用此函数之前，确保传入的`message_id`是有效的，并且在数据库中存在对应的聊天记录。
- `score`参数应在0到100之间，以确保评分的有效性。
- 在实际应用中，可能需要对用户的评分理由`reason`进行长度或内容的校验，以避免存储无效或不恰当的信息。
- 此函数通过捕获异常并记录错误信息，提高了代码的健壮性。开发者应关注日志输出，以便及时发现并处理潜在的问题。

**输出示例**:
如果用户反馈成功处理，函数可能返回如下的`BaseResponse`对象示例：
```json
{
  "code": 200,
  "msg": "已反馈聊天记录 1234567890abcdef"
}
```
如果处理过程中发生异常，函数可能返回如下的`BaseResponse`对象示例：
```json
{
  "code": 500,
  "msg": "反馈聊天记录出错：[异常信息]"
}
```
