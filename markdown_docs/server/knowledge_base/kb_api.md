## FunctionDef list_kbs
**list_kbs**: 此函数的功能是获取知识库列表。

**参数**: 此函数不接受任何参数。

**代码描述**: `list_kbs` 函数是一个无参数函数，用于从数据库中获取知识库的列表。它通过调用 `list_kbs_from_db` 函数来实现这一功能。`list_kbs_from_db` 函数从数据库中查询满足特定条件的知识库名称列表，并返回这些名称。然后，`list_kbs` 函数将这些名称封装在 `ListResponse` 类的实例中返回。`ListResponse` 类是专门用于封装列表数据响应的类，它继承自 `BaseResponse` 类，能够提供状态码、状态消息以及数据列表。这样的设计使得 API 的响应格式保持一致，便于前端开发者理解和使用。

**注意**:
- `list_kbs` 函数依赖于 `list_kbs_from_db` 函数正确地从数据库中获取知识库名称列表。因此，确保数据库连接和查询逻辑正确是使用此函数的前提。
- 返回的 `ListResponse` 实例中包含的数据列表应正确反映数据库中的知识库情况。这要求 `list_kbs_from_db` 函数能准确地执行其查询逻辑。
- 在实际部署和使用时，应注意数据库的性能和响应时间，尤其是在知识库数量较多的情况下，以保证良好的用户体验。

**输出示例**:
假设数据库中存在三个知识库，名称分别为 "知识库A", "知识库B", "知识库C"，则函数可能返回的 `ListResponse` 实例如下所示：
```
{
    "code": 200,
    "msg": "success",
    "data": ["知识库A", "知识库B", "知识库C"]
}
```
这表示 API 调用成功，且返回了包含三个知识库名称的列表。
## FunctionDef create_kb(knowledge_base_name, vector_store_type, embed_model)
**create_kb**: 此函数用于创建一个新的知识库。

**参数**:
- `knowledge_base_name`: 知识库的名称，类型为字符串。通过示例参数可以提供默认示例值。
- `vector_store_type`: 向量存储类型，类型为字符串，默认值为"faiss"。
- `embed_model`: 嵌入模型的名称，类型为字符串，默认使用项目配置的嵌入模型。

**代码描述**:
此函数首先通过调用`validate_kb_name`函数验证知识库名称的合法性。如果名称不合法或为空，则分别返回403和404状态码的`BaseResponse`对象，提示错误信息。接下来，使用`KBServiceFactory.get_service_by_name`方法检查是否已存在同名的知识库，如果存在，则返回404状态码的`BaseResponse`对象，提示知识库已存在。如果验证通过，函数将通过`KBServiceFactory.get_service`方法获取对应的知识库服务实例，并调用该实例的`create_kb`方法创建知识库。如果在创建过程中发生异常，将记录错误信息并返回500状态码的`BaseResponse`对象。成功创建知识库后，返回200状态码的`BaseResponse`对象，提示已新增知识库。

**注意**:
- 在调用此函数创建知识库之前，需要确保知识库名称不为空且不包含非法字符，以避免安全风险。
- 向量存储类型和嵌入模型应根据项目需求和配置进行选择，以确保知识库的正确创建和后续操作的有效性。
- 在处理异常时，应注意记录详细的错误信息，以便于问题的定位和解决。

**输出示例**:
如果成功创建名为"技术文档库"的知识库，函数将返回以下`BaseResponse`对象：
```
{
    "code": 200,
    "msg": "已新增知识库 技术文档库"
}
```
如果尝试创建一个已存在的知识库，例如名为"技术文档库"，函数将返回：
```
{
    "code": 404,
    "msg": "已存在同名知识库 技术文档库"
}
```
如果知识库名称不合法，将返回：
```
{
    "code": 403,
    "msg": "Don't attack me"
}
```
## FunctionDef delete_kb(knowledge_base_name)
**delete_kb**: 此函数的功能是删除指定的知识库。

**参数**:
- `knowledge_base_name`: 字符串类型，表示要删除的知识库的名称。此参数通过请求体传入，且提供了示例值 "samples"。

**代码描述**:
`delete_kb` 函数首先验证知识库名称的合法性。如果名称不合法，即不通过 `validate_kb_name` 函数的验证，将返回一个状态码为403的 `BaseResponse` 对象，消息内容为 "Don't attack me"，表示请求被拒绝。接着，函数对知识库名称进行URL解码，以确保名称的正确性。

通过 `KBServiceFactory.get_service_by_name` 方法，根据知识库名称获取对应的知识库服务实例。如果实例为 `None`，即知识库不存在，将返回一个状态码为404的 `BaseResponse` 对象，消息内容为 "未找到知识库 {knowledge_base_name}"。

若知识库服务实例获取成功，函数尝试调用知识库服务实例的 `clear_vs` 方法来清除知识库中的向量数据，然后调用 `drop_kb` 方法删除知识库。如果删除操作成功，将返回一个状态码为200的 `BaseResponse` 对象，消息内容为 "成功删除知识库 {knowledge_base_name}"。

如果在删除过程中发生异常，将捕获异常并记录错误日志，然后返回一个状态码为500的 `BaseResponse` 对象，消息内容为 "删除知识库时出现意外： {e}"，其中 `{e}` 是异常信息。

**注意**:
- 在调用此函数之前，确保传入的知识库名称是经过URL编码的。
- 此函数依赖于 `validate_kb_name` 函数来验证知识库名称的合法性，以防止潜在的安全风险。
- 删除知识库是一个不可逆的操作，一旦执行，知识库中的所有数据将被永久删除。

**输出示例**:
如果尝试删除一个不存在的知识库 "unknown_kb"，函数可能返回的 `BaseResponse` 对象如下：
```
{
    "code": 404,
    "msg": "未找到知识库 unknown_kb"
}
```
如果成功删除名为 "samples" 的知识库，函数可能返回的 `BaseResponse` 对象如下：
```
{
    "code": 200,
    "msg": "成功删除知识库 samples"
}
```
