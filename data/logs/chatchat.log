2025-07-21 18:26:30.153 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:26:30.153 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:26:30.170 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:30.172 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:30.173 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:30.174 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:31.916 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:26:31.916 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:26:31.926 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:31.928 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:31.929 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:31.931 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:26:31.932 | ERROR    | chatchat.init_database:worker:61 - (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 18:27:45.376 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:27:45.377 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:27:45.386 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:27:45.387 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:27:45.388 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:11.853 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:29:11.853 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:29:11.865 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:11.866 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:11.868 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:11.869 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:13.636 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:29:13.636 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:29:13.646 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:13.647 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:13.649 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:13.650 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:13.651 | ERROR    | chatchat.init_database:worker:61 - (sqlite3.OperationalError) unable to open database file
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 18:29:47.005 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:29:47.005 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:29:47.015 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:47.016 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:47.017 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:47.894 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:29:47.895 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:29:49.507 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:29:49.508 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:29:49.513 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:29:50.353 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.355 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.364 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.364 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.365 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.365 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.367 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.500 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.598 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:29:50.650 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.653 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.654 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.655 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:29:50.862 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='ollama', platform_type='ollama', api_base_url='http://127.0.0.1:11434/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['qwen:7b', 'qwen2:7b'], embed_models=['quentinz/bge-large-zh-v1.5'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='oneapi', platform_type='oneapi', api_base_url='http://127.0.0.1:3000/v1', api_key='sk-', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['chatglm_pro', 'chatglm_turbo', 'chatglm_std', 'chatglm_lite', 'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext', 'ERNIE-Bot', 'ERNIE-Bot-turbo', 'ERNIE-Bot-4', 'SparkDesk'], embed_models=['text-embedding-v1', 'Embedding-V1'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='openai', platform_type='openai', api_base_url='https://api.openai.com/v1', api_key='sk-proj-', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['gpt-4o', 'gpt-3.5-turbo'], embed_models=['text-embedding-3-small', 'text-embedding-3-large'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:30:31.737 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:30:31.738 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:30:35.398 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:30:35.398 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:35.408 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:35.409 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:35.410 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:36.305 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:30:36.306 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:30:37.996 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:30:37.996 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:38.002 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:38.651 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:30:38.652 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:38.662 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.663 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.664 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.777 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.778 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.785 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.785 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.785 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.785 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.788 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.861 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.918 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:38.955 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.957 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.958 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:38.959 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:39.171 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='ollama', platform_type='ollama', api_base_url='http://127.0.0.1:11434/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['qwen:7b', 'qwen2:7b'], embed_models=['quentinz/bge-large-zh-v1.5'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='oneapi', platform_type='oneapi', api_base_url='http://127.0.0.1:3000/v1', api_key='sk-', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['chatglm_pro', 'chatglm_turbo', 'chatglm_std', 'chatglm_lite', 'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext', 'ERNIE-Bot', 'ERNIE-Bot-turbo', 'ERNIE-Bot-4', 'SparkDesk'], embed_models=['text-embedding-v1', 'Embedding-V1'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='openai', platform_type='openai', api_base_url='https://api.openai.com/v1', api_key='sk-proj-', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['gpt-4o', 'gpt-3.5-turbo'], embed_models=['text-embedding-3-small', 'text-embedding-3-large'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:30:39.584 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:30:39.584 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:30:41.140 | WARNING  | chatchat.server.utils:detect_xf_models:104 - auto_detect_model needs xinference-client installed. Please try "pip install xinference-client". 
2025-07-21 18:30:41.141 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:41.145 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='ollama', platform_type='ollama', api_base_url='http://127.0.0.1:11434/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['qwen:7b', 'qwen2:7b'], embed_models=['quentinz/bge-large-zh-v1.5'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='oneapi', platform_type='oneapi', api_base_url='http://127.0.0.1:3000/v1', api_key='sk-', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['chatglm_pro', 'chatglm_turbo', 'chatglm_std', 'chatglm_lite', 'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext', 'ERNIE-Bot', 'ERNIE-Bot-turbo', 'ERNIE-Bot-4', 'SparkDesk'], embed_models=['text-embedding-v1', 'Embedding-V1'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[]), PlatformConfig(platform_name='openai', platform_type='openai', api_base_url='https://api.openai.com/v1', api_key='sk-proj-', api_proxy='', api_concurrencies=5, auto_detect_model=False, llm_models=['gpt-4o', 'gpt-3.5-turbo'], embed_models=['text-embedding-3-small', 'text-embedding-3-large'], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:30:42.540 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:42.541 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:42.542 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:43.471 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:43.473 | WARNING  | chatchat.server.utils:get_default_embedding:214 - default embedding model bge-m3 is not found in available embeddings, using quentinz/bge-large-zh-v1.5 instead
2025-07-21 18:30:43.621 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:43.654 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:43.654 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:43.654 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:43.655 | WARNING  | chatchat.server.utils:get_default_llm:205 - default llm model glm4-chat is not found in available llms, using qwen:7b instead
2025-07-21 18:30:51.493 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:30:51.493 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:30:52.036 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:30:52.036 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:32:43.154 | ERROR    | chatchat.server.db.db_handler:get_platform_model_by_system:134 - 查询失败: no such table: platform_info
2025-07-21 18:32:43.155 | ERROR    | chatchat.server.db.db_handler:get_default_model:179 - 查询默认模型失败: no such table: platform_info
2025-07-21 18:32:51.811 | ERROR    | chatchat.server.db.db_handler:get_platform_model_by_system:134 - 查询失败: no such table: platform_info
2025-07-21 18:32:51.812 | ERROR    | chatchat.server.db.db_handler:get_default_model:179 - 查询默认模型失败: no such table: platform_info
2025-07-21 18:33:31.076 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:33:31.078 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:33:34.045 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:33:34.577 | ERROR    | __main__:start_main_server:310 - 'API Server (14628)'
2025-07-21 18:33:34.577 | WARNING  | __main__:start_main_server:311 - Caught KeyboardInterrupt! Setting stop event...
2025-07-21 18:33:34.577 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:33:34.577 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:34:06.599 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:34:06.600 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:34:09.522 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:34:15.771 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:34:15.773 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:34:17.402 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:37:42.552 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:37:42.552 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:37:42.668 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:37:42.669 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:37:47.662 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:37:47.664 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:37:50.718 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:37:52.434 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:37:52.436 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:37:54.055 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:38:48.993 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:38:48.993 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:38:51.135 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 18:38:51.135 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 18:38:52.872 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:38:52.874 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:38:54.542 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:38:54.698 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 18:38:54.700 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 18:38:57.931 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 18:39:02.632 | ERROR    | chatchat.server.db.db_handler:get_chat_history:1151 - 操作失败: no such table: shop_infoshop
2025-07-21 18:39:02.633 | ERROR    | chatchat.server.db.db_handler:get_chat_history:1151 - 操作失败: no such table: shop_infoshop
2025-07-21 18:39:04.367 | ERROR    | chatchat.server.db.db_handler:get_chat_history:1151 - 操作失败: no such table: shop_infoshop
2025-07-21 18:39:04.475 | ERROR    | chatchat.server.db.db_handler:get_chat_history:1151 - 操作失败: no such table: shop_infoshop
2025-07-21 18:39:13.189 | ERROR    | chatchat.server.db.db_handler:get_chat_history:1151 - 操作失败: no such table: shop_infoshop
2025-07-21 18:39:13.292 | ERROR    | chatchat.server.db.db_handler:get_chat_history:1151 - 操作失败: no such table: shop_infoshop
2025-07-21 19:47:56.377 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 19:47:56.378 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 19:47:59.943 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 19:47:59.945 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 19:48:02.939 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 19:48:04.713 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 19:48:04.714 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 19:48:08.214 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 19:48:08.215 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 19:48:09.833 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 20:34:41.533 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 20:34:41.533 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 20:34:44.851 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 20:34:44.853 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 20:34:47.849 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 20:34:49.098 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 20:34:49.098 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 20:47:07.313 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 20:47:07.315 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 20:47:08.903 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 21:15:26.653 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 21:15:26.654 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 21:15:30.242 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 21:15:30.244 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 21:15:31.887 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 21:27:34.827 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 21:27:34.827 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 21:27:38.211 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 21:27:38.213 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 21:27:39.893 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 21:45:35.146 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 21:45:36.119 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 21:45:36.190 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 21:45:36.300 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 21:52:12.431 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 21:52:12.433 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 21:52:15.845 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 21:58:30.853 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 21:58:30.855 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 21:58:32.510 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:12:47.035 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:12:47.036 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:12:51.990 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:12:51.992 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:12:53.647 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:14:24.628 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:14:24.629 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:14:28.274 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:14:28.276 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:14:29.881 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:15:42.163 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:15:42.164 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:15:45.573 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:15:45.574 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:15:47.174 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:16:54.135 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:16:54.135 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:16:57.527 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:16:57.529 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:16:59.130 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:17:10.381 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-21 22:17:49.382 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:17:49.382 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:17:53.065 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:17:53.066 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:17:54.805 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:21:44.917 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:21:44.917 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:21:48.378 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:21:48.380 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:21:50.020 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:23:55.697 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:23:55.697 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:23:59.265 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:23:59.266 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:24:00.964 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:24:38.533 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:24:38.533 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:24:42.173 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:24:42.174 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:24:43.808 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:26:20.543 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:26:20.544 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:26:24.109 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:26:24.111 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:26:25.749 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:27:55.373 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:27:55.374 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:27:58.955 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:27:58.957 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:28:00.688 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:28:42.059 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:28:42.059 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:28:45.581 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:28:45.583 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:28:47.326 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:29:16.912 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:29:16.912 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:29:20.602 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:29:20.603 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:29:22.305 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:30:25.132 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:30:25.132 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:30:28.551 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:30:28.553 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:30:30.155 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:50:20.921 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:50:20.922 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:50:24.306 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:50:24.308 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:50:25.938 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:51:15.019 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:51:15.019 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:51:18.465 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:51:18.467 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:51:20.135 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:52:19.636 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:52:19.637 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:52:23.049 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:52:23.051 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:52:24.674 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:53:11.237 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:53:11.238 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:53:14.738 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:53:14.740 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:53:16.372 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:53:41.203 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:53:41.204 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:53:44.620 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:53:44.622 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:53:46.253 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 22:57:33.072 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 22:57:33.073 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 22:57:36.549 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-21 22:57:36.550 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 22:57:38.205 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 23:02:06.363 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-21 23:02:06.363 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-21 23:02:09.711 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-21 23:02:09.712 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 23:02:11.411 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 23:45:09.923 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-21 23:45:09.924 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-21 23:48:44.264 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-21 23:48:44.266 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 23:48:46.040 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 23:50:16.501 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-21 23:50:16.501 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-21 23:50:20.042 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-21 23:50:20.044 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 23:50:21.745 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 23:51:14.164 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-21 23:51:14.165 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-21 23:51:17.900 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-21 23:51:17.903 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 23:51:19.692 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 23:54:50.854 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-21 23:54:50.855 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-21 23:54:54.511 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-21 23:54:54.514 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 23:54:56.287 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-21 23:57:29.448 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-21 23:57:29.448 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-21 23:57:33.071 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-21 23:57:33.073 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-21 23:57:34.820 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:02:07.986 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:02:07.986 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:02:11.629 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:02:11.631 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:02:13.404 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:04:07.068 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:04:07.068 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:04:10.629 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:04:10.631 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:04:11.262 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-22 00:04:11.262 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-22 00:04:12.485 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:04:15.295 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:04:15.297 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:04:17.213 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 00:04:18.605 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:04:35.761 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-22 00:07:24.642 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:07:24.642 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:07:28.198 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:07:28.200 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:07:31.466 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:10:28.973 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:10:28.974 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:10:32.639 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:10:32.641 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:10:34.442 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:11:10.206 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:11:10.206 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:11:13.740 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:11:13.742 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:11:15.476 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:13:22.833 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:13:22.834 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:13:26.479 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:13:26.482 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:13:28.228 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:17:01.664 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:17:01.664 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:17:06.262 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:17:06.264 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:17:08.042 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:19:13.582 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-22 00:21:46.297 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:21:46.298 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:21:50.102 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:21:50.104 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:21:51.853 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:23:03.727 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:23:03.728 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:23:07.756 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:23:07.757 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:23:09.446 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:27:54.897 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:27:54.897 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:27:58.738 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:27:58.739 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:28:00.523 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:30:22.587 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:30:22.588 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:30:26.450 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:30:26.453 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:30:28.216 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:34:08.377 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:34:08.378 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:34:12.122 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:34:12.124 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:34:13.945 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:36:00.923 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:36:00.924 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:36:04.525 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:36:04.527 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:36:06.280 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:37:07.256 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:37:07.256 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:37:10.811 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:37:10.812 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:37:12.559 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:40:50.087 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:40:50.088 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:40:53.791 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:40:53.793 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:40:55.536 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:42:59.485 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:42:59.485 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:43:03.265 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:43:03.267 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:43:05.022 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:44:52.604 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:44:52.604 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:44:56.940 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:44:56.942 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:44:58.689 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:45:55.219 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:45:55.220 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:45:59.020 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:45:59.022 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:46:00.810 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:47:48.594 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:47:48.595 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:47:52.558 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:47:52.560 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:47:54.313 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:48:12.857 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:48:12.858 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:48:16.392 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:48:16.393 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:48:18.107 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:50:23.541 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:50:23.542 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:50:27.138 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:50:27.141 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:50:28.877 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:54:27.891 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:54:27.892 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:54:31.607 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:54:31.609 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:54:33.416 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:55:23.741 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:55:23.741 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:55:27.219 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:55:27.221 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:55:28.925 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 00:59:33.027 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 00:59:33.027 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 00:59:36.668 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 00:59:36.670 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 00:59:38.418 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 01:03:08.712 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 01:03:08.712 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 01:03:12.867 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 01:03:12.868 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 01:03:14.610 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 13:58:32.776 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 13:58:32.783 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 13:58:36.411 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 13:58:36.413 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 13:58:38.077 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 13:59:35.964 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 13:59:35.965 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 13:59:39.296 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 13:59:39.298 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 13:59:40.973 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:02:21.074 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:02:21.075 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:02:24.507 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:02:24.509 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:02:26.140 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:07:15.514 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:07:15.515 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:07:18.780 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:07:18.782 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:07:20.391 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:07:53.685 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:07:53.686 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:07:56.987 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:07:56.988 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:07:58.582 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:09:19.978 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe_doubao/vector_store/doubao-embedding-large-text-250515' from disk.
2025-07-22 14:09:19.996 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: doubao-embedding-large-text-250515.
2025-07-22 14:09:57.544 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:09:57.544 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:10:00.845 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:10:00.847 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:10:02.466 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:10:06.251 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:10:06.260 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:10:09.718 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:10:09.720 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:10:12.662 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:11:56.969 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:11:56.970 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:11:59.939 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:11:59.939 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:12:00.306 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:12:00.308 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:12:03.342 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:12:03.344 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:12:03.433 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:12:04.980 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:14:26.395 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:14:26.396 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:14:29.758 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:14:29.760 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:14:31.379 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:27:26.760 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:27:26.761 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:27:29.930 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:27:29.932 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:27:31.535 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:29:41.647 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:29:41.648 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:29:44.928 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:29:44.930 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:29:46.620 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:31:44.281 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:31:44.282 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:31:47.731 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:31:47.733 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:31:49.385 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:34:06.655 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:34:06.656 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:34:10.007 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:34:10.009 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:34:11.702 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:45:41.733 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:45:41.733 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:45:45.100 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:45:45.102 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:45:46.938 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:49:55.479 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:49:55.480 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:49:58.726 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:49:58.728 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:50:00.358 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:51:56.148 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 14:51:56.148 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 14:51:59.443 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 14:51:59.445 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 14:52:01.054 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 14:56:40.387 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-22 15:02:35.227 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 15:02:35.228 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 15:02:38.536 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 15:02:38.538 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 15:02:40.169 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 17:26:12.510 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 17:26:12.511 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 17:26:15.988 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 17:26:15.990 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 17:26:17.586 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 18:18:03.969 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 18:18:03.970 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 18:18:07.696 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 18:18:07.698 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 18:18:09.368 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 18:40:39.660 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 18:40:39.670 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 18:40:45.518 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 18:40:45.518 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 18:40:45.549 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 18:40:45.552 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 18:40:48.804 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 18:40:48.864 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 18:40:48.865 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 18:40:50.463 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 18:41:01.338 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-22 18:41:16.851 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-22 18:41:17.134 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\shoe\content\洗鞋主推39.9.txt
2025-07-22 18:41:20.864 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('shoe', 'text-embedding-v4') 保存到磁盘
2025-07-22 19:08:26.103 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:08:26.104 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:08:29.457 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:08:29.459 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:08:31.085 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:17:25.969 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:17:25.969 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:17:25.983 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:17:29.255 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:17:29.255 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:17:29.257 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:17:32.348 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:17:32.349 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:17:32.352 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:23:24.664 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:23:24.665 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:23:28.385 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:23:28.387 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:23:30.217 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:23:36.808 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:23:36.819 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:23:40.535 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:23:40.536 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:23:43.799 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:24:19.819 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:24:19.819 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:24:19.824 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:24:22.149 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe/vector_store/text-embedding-v4' from disk.
2025-07-22 19:24:22.149 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:24:23.049 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:24:23.050 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:24:23.052 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:25:03.155 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '鞋子/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 19:25:03.893 | ERROR    | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:140 - Error in __cdecl faiss::FileIOWriter::FileIOWriter(const char *) at D:\a\faiss-wheels\faiss-wheels\faiss\faiss\impl\io.cpp:98: Error: 'f' failed: could not open data\knowledge_base\鞋子\vector_store\doubao-embedding-text-240715\index.faiss for writing: No such file or directory
2025-07-22 19:25:03.894 | ERROR    | chatchat.server.knowledge_base.kb_api:create_kb:44 - RuntimeError: 创建知识库出错： 向量库 鞋子 加载失败。
2025-07-22 19:25:14.788 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\鞋子\content\洗鞋(1).txt
2025-07-22 19:25:36.786 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\鞋子\content\洗鞋(1).txt
2025-07-22 19:30:14.764 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:30:14.794 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\鞋子\content\洗鞋(1).txt
2025-07-22 19:30:36.781 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:30:36.808 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\鞋子\content\洗鞋(1).txt
2025-07-22 19:31:17.327 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:33:58.691 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\temp\c255db594f1245c6877af6cd2557881c\洗鞋(1).txt
2025-07-22 19:33:58.714 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:162 - loading vector store in 'c255db594f1245c6877af6cd2557881c' to memory.
2025-07-22 19:35:14.771 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:35:14.799 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\鞋子\content\洗鞋(1).txt
2025-07-22 19:35:36.788 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:35:36.816 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\鞋子\content\洗鞋(1).txt
2025-07-22 19:36:02.054 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/create_knowledge_base: timed out
2025-07-22 19:36:17.308 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:36:17.341 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:40:14.785 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:40:14.786 | ERROR    | chatchat.webui_pages.utils:to_json:232 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-22 19:40:36.797 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:40:36.798 | ERROR    | chatchat.webui_pages.utils:to_json:232 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-22 19:41:17.317 | ERROR    | chatchat.webui_pages.utils:post:86 - ReadTimeout: error when post /knowledge_base/upload_docs: timed out
2025-07-22 19:41:17.340 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:41:17.393 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:41:17.395 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:43:15.835 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:43:15.835 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:43:19.712 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:43:19.714 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:43:21.634 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:44:18.134 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:44:18.135 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:44:18.137 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:44:23.607 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:44:23.608 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:44:23.611 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:44:31.693 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: text-embedding-v4.
2025-07-22 19:44:31.694 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'text-embedding-v4': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:44:31.695 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:44:51.791 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:44:51.791 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:44:54.755 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:44:54.755 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:44:55.713 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:44:55.716 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:44:57.638 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:44:58.733 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:44:58.735 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:45:02.254 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 19:45:02.305 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:45:59.822 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'shoe_doubao/vector_store/doubao-embedding-large-text-250515' from disk.
2025-07-22 19:45:59.830 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: doubao-embedding-large-text-250515.
2025-07-22 19:46:03.392 | ERROR    | chatchat.server.utils:get_Embeddings:364 - failed to create Embeddings for model: doubao-embedding-large-text-250515.
2025-07-22 19:46:03.392 | ERROR    | chatchat.server.utils:check_embed_model:378 - failed to access embed model 'doubao-embedding-large-text-250515': 'NoneType' object has no attribute 'embed_query'
2025-07-22 19:46:03.397 | ERROR    | chatchat.webui_pages.utils:ret_sync:207 - RemoteProtocolError: API通信遇到错误：peer closed connection without sending complete message body (incomplete chunked read)
2025-07-22 19:47:11.752 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 19:47:29.251 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:47:33.654 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 19:47:38.727 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 19:47:39.621 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:47:40.865 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 19:48:46.353 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:48:46.353 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:48:50.318 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:48:50.320 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:48:52.265 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:49:31.527 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\变量提示词.txt
2025-07-22 19:49:32.487 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 19:49:36.321 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 19:49:36.846 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\变量提示词.txt
2025-07-22 19:49:36.846 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:49:38.298 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 19:53:19.406 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:53:19.407 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:53:23.229 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:53:23.231 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:53:24.311 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 19:53:24.312 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 19:53:25.219 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:53:28.364 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 19:53:28.366 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 19:53:29.864 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 19:53:31.797 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 19:53:31.894 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 19:54:20.958 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 19:54:44.059 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 19:54:44.296 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\变量提示词.txt
2025-07-22 19:54:44.296 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 19:54:48.753 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 20:05:47.952 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 20:05:47.953 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 20:05:50.726 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 20:05:50.726 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 20:05:51.683 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 20:05:51.685 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 20:05:53.598 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 20:05:54.490 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 20:05:54.492 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 20:05:57.877 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 20:07:53.306 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 20:07:54.042 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\变量提示词.txt
2025-07-22 20:07:54.042 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\洗鞋(1).txt
2025-07-22 20:07:58.830 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 20:13:35.781 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 20:13:35.782 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 20:13:39.658 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 20:13:39.660 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 20:13:41.491 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 20:22:48.473 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 20:22:48.473 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 20:22:52.793 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 20:22:52.795 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 20:22:54.769 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 20:25:58.317 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 20:25:58.318 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 20:26:02.134 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 20:26:02.135 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 20:26:03.952 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:03:15.294 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:03:15.298 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:03:17.840 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:03:23.869 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:03:23.876 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:03:24.979 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:03:27.014 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:03:29.043 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:03:29.043 | ERROR    | chatchat.webui_pages.utils:to_json:232 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-22 21:03:32.288 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:08:52.344 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:08:52.346 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 21:09:00.294 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:09:00.300 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:09:01.544 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:09:03.584 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:09:05.621 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:09:05.621 | ERROR    | chatchat.webui_pages.utils:to_json:232 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-22 21:09:06.649 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:10:37.504 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:10:37.505 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 21:10:40.963 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:10:40.965 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:10:42.534 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:10:47.389 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:10:47.390 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 21:10:54.425 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:10:54.431 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:10:58.329 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:12:14.272 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:12:14.272 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 21:12:17.841 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:12:17.841 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 21:12:21.302 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:12:21.308 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:12:24.623 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:12:25.020 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-22 21:12:25.026 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-22 21:12:30.020 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-22 21:12:31.160 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-22 21:12:45.686 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-22 21:16:42.597 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\history_id_635_20250722.txt
2025-07-22 21:16:48.202 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 21:16:59.066 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\history_id_634_20250722.txt
2025-07-22 21:16:59.845 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 21:17:06.624 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\history_id_626_20250722.txt
2025-07-22 21:17:07.383 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 21:17:25.336 | INFO     | chatchat.server.knowledge_base.utils:file2docs:346 - UnstructuredFileLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\history_id_59_20250722.txt
2025-07-22 21:17:26.140 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-22 21:33:02.280 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:33:02.281 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-22 21:33:02.827 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-22 21:33:02.827 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:04:58.204 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:04:58.211 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:05:05.616 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:05:05.619 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:05:05.719 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:05:08.723 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:05:26.643 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-23 16:16:05.069 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:16:05.070 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:16:08.635 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:16:08.637 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:16:10.266 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:17:04.275 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:17:29.570 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:18:47.317 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:18:47.317 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:18:50.930 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:18:50.932 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:18:52.511 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:33:46.116 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:33:46.116 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:33:49.534 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:33:49.536 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:33:51.170 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:34:58.923 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:35:38.135 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:35:38.135 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:35:41.563 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:35:41.565 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:35:43.120 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:36:47.093 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:37:17.346 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:38:19.670 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:05.780 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:43:05.780 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:43:09.236 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:43:09.238 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:43:10.854 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:43:48.163 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:50.950 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:52.780 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:57.116 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:57.982 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:58.182 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:43:59.831 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:44:49.069 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:44:49.069 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:44:49.107 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:44:53.048 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:44:53.050 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:44:54.669 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:45:11.407 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:51:15.449 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:51:15.450 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:51:19.060 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:51:19.062 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:51:20.708 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:55:49.160 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:55:49.160 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:55:52.607 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:55:52.609 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:55:54.359 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 16:56:50.022 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 16:56:50.023 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 16:56:50.046 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 16:56:53.378 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 16:56:53.380 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 16:56:55.005 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:12:10.620 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 17:12:10.622 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 17:12:12.260 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:12:20.820 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:12:22.572 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:12:24.251 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:12:26.876 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:12:27.466 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:12:28.042 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:37:29.193 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 17:37:29.194 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 17:37:32.605 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 17:37:32.607 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 17:37:34.235 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:37:45.327 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:37:45.662 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:37:46.359 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:37:59.505 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:38:00.574 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:38:01.573 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:38:08.180 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:38:49.929 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:49:20.773 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 17:49:20.773 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 17:49:24.241 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 17:49:24.242 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 17:49:25.868 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:50:37.198 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:51:32.850 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 17:51:32.850 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 17:51:36.308 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 17:51:36.310 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 17:51:37.923 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:51:43.309 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:51:44.676 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:51:45.912 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:51:45.972 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:52:48.256 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 17:52:48.257 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 17:52:51.769 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 17:52:51.771 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 17:52:53.416 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:52:59.977 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:53:00.411 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:53:01.594 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:54:23.556 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:56:11.226 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 17:56:11.226 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 17:56:14.739 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 17:56:14.741 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 17:56:16.360 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 17:56:23.515 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:56:23.622 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:56:27.255 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:59:05.013 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 17:59:39.268 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 18:02:36.609 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 18:02:36.615 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 18:02:44.453 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 18:02:44.459 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 18:02:50.456 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 18:03:11.739 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-23 20:39:39.010 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 20:39:39.020 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 20:39:40.087 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 20:39:40.099 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 20:53:58.800 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 20:53:58.802 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 20:54:00.501 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 20:54:00.503 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 20:54:02.504 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 20:54:03.133 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 20:58:44.612 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-23 21:20:35.498 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 21:20:35.499 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 21:20:39.059 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 21:20:39.061 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 21:20:40.979 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 21:20:47.732 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:20:48.709 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:20:49.415 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:20:50.753 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:20:51.304 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:21:37.412 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 21:21:37.412 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 21:21:40.812 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 21:21:40.814 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 21:21:42.400 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 21:23:30.403 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 21:23:30.403 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 21:23:33.759 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 21:23:33.761 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 21:23:35.344 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 21:23:41.563 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:23:41.627 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:23:41.995 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:23:43.032 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:23:44.345 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:23:57.895 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 21:23:57.896 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 21:24:31.815 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 21:24:31.816 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 21:24:33.405 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 21:27:53.451 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 21:27:53.452 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 21:27:56.919 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 21:27:56.920 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 21:27:58.514 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 21:28:03.910 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:28:04.600 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:28:06.197 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:28:08.734 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:51:02.457 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:51:02.529 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 21:51:03.285 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:256 - streaming progress has been interrupted by user.
2025-07-23 22:37:47.448 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 22:37:47.448 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 22:37:51.045 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 22:37:51.047 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 22:37:52.714 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 22:39:16.054 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 22:39:16.054 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 22:39:19.527 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 22:39:19.528 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 22:39:21.152 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-23 22:40:36.249 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-23 22:40:36.250 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-23 22:40:39.681 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-23 22:40:39.683 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-23 22:40:41.255 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-24 17:50:11.376 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-24 17:50:11.397 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-24 17:50:12.740 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-07-24 17:50:12.751 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-07-24 20:05:59.378 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-24 20:05:59.382 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-24 20:06:02.391 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-07-24 20:06:02.395 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-24 20:06:04.607 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-24 20:06:10.153 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:58:42.464 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-08-01 18:58:42.471 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:58:46.141 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:58:54.402 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 18:58:56.447 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 18:58:58.471 | ERROR    | chatchat.webui_pages.utils:get:63 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 18:58:58.471 | ERROR    | chatchat.webui_pages.utils:to_json:232 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-08-01 19:01:03.106 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-08-01 19:01:03.106 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-08-01 19:01:20.982 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-08-01 19:01:20.984 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:01:24.164 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:01:40.895 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-08-01 19:01:40.897 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:01:42.504 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:01:58.759 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:16:49.095 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-08-01 19:16:49.096 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-08-01 19:16:52.687 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-08-01 19:16:52.688 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:16:54.310 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:23:34.870 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-08-01 19:23:34.871 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-08-01 19:23:38.791 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-08-01 19:23:38.795 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:23:39.776 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-08-01 19:23:39.777 | INFO     | __main__:start_main_server:326 - Process status: %s
2025-08-01 19:23:42.820 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:23:44.068 | INFO     | __main__:start_main_server:257 - 正在启动服务：
2025-08-01 19:23:44.070 | INFO     | __main__:start_main_server:258 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:23:45.854 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:24:55.309 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:28:29.607 | WARNING  | __main__:start_main_server:315 - Sending SIGKILL to %s
2025-08-01 19:28:29.607 | INFO     | __main__:start_main_server:326 - Process status: %s
