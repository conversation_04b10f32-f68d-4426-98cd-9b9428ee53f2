from datetime import datetime
import uuid
from typing import List, Dict

import openai
import streamlit as st
import streamlit_antd_components as sac
from streamlit_chatbox import *
from streamlit_extras.bottom_container import bottom

from chatchat.server.db.db_handler import get_platform_prompt_name

from chatchat.server.knowledge_base.utils import LOADER_DICT
from chatchat.server.utils import get_config_models, get_config_platforms, get_default_llm, api_address, get_default_kb
from chatchat.webui_pages.dialogue.dialogue import (save_session, restore_session, rerun,
                                                    get_messages_history, upload_temp_docs,
                                                    add_conv, del_conv, clear_conv)
from chatchat.webui_pages.utils import *


chat_box = ChatBox(assistant_avatar=get_img_base64("chatchat_icon_blue_square_v2.png"))


def init_widgets():
    st.session_state.setdefault("history_len", Settings.model_settings.HISTORY_LEN)
    st.session_state.setdefault("selected_kb", get_default_kb())
    st.session_state.setdefault("kb_top_k", Settings.kb_settings.VECTOR_SEARCH_TOP_K)
    st.session_state.setdefault("se_top_k", Settings.kb_settings.SEARCH_ENGINE_TOP_K)
    st.session_state.setdefault("score_threshold", Settings.kb_settings.SCORE_THRESHOLD)
    st.session_state.setdefault("search_engine", Settings.kb_settings.DEFAULT_SEARCH_ENGINE)
    st.session_state.setdefault("return_direct", False)
    st.session_state.setdefault("cur_conv_name", chat_box.cur_chat_name)
    st.session_state.setdefault("last_conv_name", chat_box.cur_chat_name)
    st.session_state.setdefault("file_chat_id", None)


def kb_chat(api: ApiRequest):
    """增强版智能对话界面"""
    ctx = chat_box.context
    ctx.setdefault("uid", uuid.uuid4().hex)
    ctx.setdefault("file_chat_id", None)
    ctx.setdefault("temperature", Settings.model_settings.TEMPERATURE)
    init_widgets()
    kb_top_k = Settings.kb_settings.VECTOR_SEARCH_TOP_K

    # 添加聊天界面增强样式
    st.markdown(
        """
        <style>
        /* 隐藏Streamlit默认元素 */
        #MainMenu {visibility: hidden !important;}
        .stDeployButton {display: none !important;}
        footer {visibility: hidden !important;}
        .stApp > header {visibility: hidden !important;}
        .viewerBadge_container__1QSob {display: none !important;}
        .stActionButton {display: none !important;}
        [data-testid="stToolbar"] {display: none !important;}
        [data-testid="stDecoration"] {display: none !important;}
        [data-testid="stStatusWidget"] {display: none !important;}

        /* 精确隐藏header相关元素 */
        .stAppHeader {display: none !important;}
        .stAppToolbar {display: none !important;}
        .stMainMenu {display: none !important;}
        .stAppDeployButton {display: none !important;}
        .stToolbarActions {display: none !important;}
        [data-testid="stHeader"] {display: none !important;}
        [data-testid="stToolbar"] {display: none !important;}
        [data-testid="stMainMenu"] {display: none !important;}
        [data-testid="stAppDeployButton"] {display: none !important;}
        [data-testid="stToolbarActions"] {display: none !important;}

        /* 隐藏所有header相关的CSS类 */
        .st-emotion-cache-1ffuo7c {display: none !important;}
        .st-emotion-cache-14vh5up {display: none !important;}
        .st-emotion-cache-1j22a0y {display: none !important;}
        .st-emotion-cache-70qvj9 {display: none !important;}
        .st-emotion-cache-scp8yw {display: none !important;}
        .st-emotion-cache-1p1m4ay {display: none !important;}
        .st-emotion-cache-czk5ss {display: none !important;}


        /* 页面样式优化 */
        .stApp {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #f1f5f9 75%, #ffffff 100%);
        }

        /* 移除白色容器 */
        .main .block-container {
            padding-top: 1rem;
            padding-bottom: 1rem;
            background: transparent !important;
        }

        /* 配置侧边栏样式 - 粘性定位 */
        .config-section {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 16px !important;
            padding: 1.5rem !important;
            margin: 1rem 0 !important;
            border: 1px solid rgba(226, 232, 240, 0.6) !important;
            backdrop-filter: blur(15px) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            position: sticky !important;
            top: 20px !important;
            z-index: 999 !important;
            max-height: calc(100vh - 40px) !important;
            overflow-y: auto !important;
        }

        /* 配置内容样式 */
        .config-content {
            padding-top: 10px !important;
        }

        /* 确保右侧列有足够的空间 */
        div[data-testid="column"]:nth-child(2) {
            position: relative !important;
            min-height: 100vh !important;
        }

        .config-header {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        /* 确保主布局容器的稳定性 */
        div[data-testid="column"]:nth-child(2) {
            position: relative !important;
        }

        /* 聊天区域样式优化 */
        div[data-testid="column"]:nth-child(1) {
            height: 100vh !important;
            overflow-y: auto !important;
            padding-right: 1rem !important;
        }



        /* 配置面板滚动条美化 */
        .config-section::-webkit-scrollbar {
            width: 6px;
        }

        .config-section::-webkit-scrollbar-track {
            background: rgba(226, 232, 240, 0.3);
            border-radius: 3px;
        }

        .config-section::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.4);
            border-radius: 3px;
            transition: background 0.3s ease;
        }

        .config-section::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.6);
        }



        /* 聊天输入框增强 */
        .stChatInput > div {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 25px !important;
            border: 1px solid rgba(226, 232, 240, 0.8) !important;
            backdrop-filter: blur(10px) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05) !important;
            margin-left: 10px !important;
        }

        /* 输入框容器对齐 */
        div[data-testid="column"]:nth-child(2) {
            display: flex !important;
            align-items: center !important;
        }

        /* 工具按钮增强 - 统一高度和对齐 */
        .stButton > button {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 1px solid rgba(226, 232, 240, 0.8) !important;
            border-radius: 12px !important;
            backdrop-filter: blur(10px) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            font-weight: 500 !important;
            padding: 0.6rem 1rem !important;
            height: 42px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            white-space: nowrap !important;
            min-width: 100px !important;
        }

        .stButton > button:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            border-color: #667eea !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
            color: #667eea !important;
        }

        /* 按钮容器对齐 */
        .stButton {
            display: flex !important;
            justify-content: center !important;
        }

        /* 新建对话按钮特殊样式 */
        div[data-testid="column"]:first-child .stButton > button {
            width: 50px !important;
            height: 50px !important;
            min-width: 50px !important;
            border-radius: 50% !important;
            font-size: 1.2rem !important;
            padding: 0 !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
        }

        div[data-testid="column"]:first-child .stButton > button:hover {
            transform: translateY(-2px) scale(1.05) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
        }
        .stMainBlockContainer>.stVerticalBlock > div[data-testid="stLayoutWrapper"] {
            width: calc(75% - 1rem);
        }
        div[data-testid="stVerticalBlock"] {
            height: auto;
            position: sticky;
            top: 0;
        }
        </style>
        """,
        unsafe_allow_html=True
    )

    # sac on_change callbacks not working since st>=1.34
    if st.session_state.cur_conv_name != st.session_state.last_conv_name:
        save_session(st.session_state.last_conv_name)
        restore_session(st.session_state.cur_conv_name)
        st.session_state.last_conv_name = st.session_state.cur_conv_name

    llm_model = get_default_llm()

    @st.dialog("重命名会话")
    def rename_conversation():
        st.markdown("### 📝 重命名会话")
        name = st.text_input("会话名称", placeholder="请输入新的会话名称")
        col1, col2 = st.columns(2)
        if col1.button("确认", type="primary", use_container_width=True):
            chat_box.change_chat_name(name)
            restore_session()
            st.session_state["cur_conv_name"] = name
            rerun()
        if col2.button("取消", use_container_width=True):
            st.rerun()

    # 主界面布局
    col1, col2 = st.columns([3, 1])

    with col2:
        # 配置参数面板 - 开始固定容器
        st.markdown(
            """
            <div class="config-section">
                <div class="config-content">
            """,
            unsafe_allow_html=True
        )

        # 对话模式选择
        dialogue_modes = ["知识库问答", "文件对话", "搜索引擎问答"]
        dialogue_mode = st.selectbox(
            "🎯 对话模式",
            dialogue_modes,
            key="dialogue_mode",
            help="选择最适合您需求的对话模式"
        )

        placeholder = st.empty()

        # 客服模板选择
        prompt_templates_kb_list = get_platform_prompt_name('rag')
        prompt_name = st.selectbox(
            "📝 客服模板",
            prompt_templates_kb_list,
            key="prompt_name",
            help="选择专业的客服回复模板"
        )

        # 模型平台选择
        platforms = ["所有"] + list(get_config_platforms())
        platform = st.selectbox(
            "🚀 模型平台",
            platforms,
            key="platform",
            help="选择AI模型运行平台"
        )

        # LLM模型选择
        llm_models = list(get_config_models(
            model_type="llm",
            platform_name=None if platform == "所有" else platform
        ))
        llm_models += list(get_config_models(
            model_type="image2text",
            platform_name=None if platform == "所有" else platform
        ))
        llm_model = st.selectbox(
            "🧠 AI模型",
            llm_models,
            key="llm_model",
            help="选择最适合的AI语言模型"
        )

        # 高级参数配置
        with st.expander("🔧 高级参数", expanded=False):
            history_len = st.slider(
                "历史对话轮数",
                0, 20,
                st.session_state.get("history_len", 5),
                help="控制AI记忆的对话轮数"
            )
            kb_top_k = st.slider(
                "知识匹配数量",
                1, 20,
                st.session_state.get("kb_top_k", 5),
                help="从知识库中检索的相关内容数量"
            )
            score_threshold = st.slider(
                "匹配分数阈值",
                0.0, 2.0,
                st.session_state.get("score_threshold", 0.5),
                step=0.01,
                help="知识匹配的最低分数要求"
            )
            return_direct = st.checkbox(
                "仅返回检索结果",
                st.session_state.get("return_direct", False),
                help="直接返回知识库检索结果，不进行AI加工"
            )

        def on_kb_change():
            st.toast(f"✅ 已加载知识库：{st.session_state.selected_kb}", icon="📚")

        # 根据对话模式显示相应配置
        with placeholder.container():
            if dialogue_mode == "知识库问答":
                kb_list = [x["kb_name"] for x in api.list_knowledge_bases()]
                selected_kb = st.selectbox(
                    "📚 选择知识库",
                    kb_list,
                    on_change=on_kb_change,
                    key="selected_kb",
                    help="选择要查询的知识库"
                )
                if selected_kb:
                    st.success(f"当前知识库：{selected_kb}")

            elif dialogue_mode == "文件对话":
                st.markdown("📁 **文件上传**")
                files = st.file_uploader(
                    "上传知识文件",
                    [i for ls in LOADER_DICT.values() for i in ls],
                    accept_multiple_files=True,
                    help="支持PDF、Word、TXT等多种格式"
                )
                if files:
                    st.info(f"已选择 {len(files)} 个文件")
                if st.button("🚀 开始上传", disabled=len(files) == 0, type="primary"):
                    with st.spinner("正在上传文件..."):
                        st.session_state["file_chat_id"] = upload_temp_docs(files, api)
                    st.success("文件上传成功！")

            elif dialogue_mode == "搜索引擎问答":
                search_engine_list = list(Settings.tool_settings.search_internet["search_engine_config"])
                search_engine = st.selectbox(
                    "🔍 选择搜索引擎",
                    search_engine_list,
                    key="search_engine",
                    help="选择用于网络搜索的引擎"
                )
                if search_engine:
                    st.info(f"当前搜索引擎：{search_engine}")

        # 关闭固定配置面板容器
        st.markdown(
            """
                </div>
            </div>
            """,
            unsafe_allow_html=True
        )

    with col1:
        # 聊天主界面 - 无容器，直接显示
        chat_box.output_messages()

        # 聊天输入区域
        chat_input_placeholder = "💬 请输入您的问题，我将为您提供专业的解答..."

        # 底部输入框 - 极简版
        with bottom():
            # 输入框和新建对话按钮
            input_col1, input_col2 = st.columns([1, 10])

            with input_col1:
                if st.button("💬", help="新建对话", key="new_chat"):
                    chat_box.reset_history()
                    st.toast("✅ 已开始新对话", icon="💬")
                    rerun()

            with input_col2:
                prompt = st.chat_input(chat_input_placeholder, key="prompt")
    if prompt:
        history = get_messages_history(st.session_state.get('history_len'))
        messages = history + [{"role": "user", "content": prompt}]
        chat_box.user_say(prompt)

        extra_body = dict(
            top_k=kb_top_k,
            score_threshold=st.session_state.score_threshold,
            temperature=ctx.get("temperature"),
            prompt_name=prompt_name,
            return_direct=st.session_state.return_direct,
        )

        api_url = api_address(is_public=True)
        if dialogue_mode == "知识库问答":
            print(123)
            client = openai.Client(base_url=f"{api_url}/knowledge_base/local_kb/{selected_kb}", api_key="NONE")
            chat_box.ai_say([
                Markdown("...", in_expander=True, title="知识库匹配结果", state="running", expanded=st.session_state.return_direct),
                f"正在查询知识库 `{selected_kb}` ...",
            ])
        elif dialogue_mode == "文件对话":
            if st.session_state.get("file_chat_id") is None:
                st.error("请先上传文件再进行对话")
                st.stop()
            knowledge_id = st.session_state.get("file_chat_id")
            client = openai.Client(base_url=f"{api_url}/knowledge_base/temp_kb/{knowledge_id}", api_key="NONE")
            chat_box.ai_say([
                Markdown("...", in_expander=True, title="知识库匹配结果", state="running", expanded=st.session_state.return_direct),
                f"正在查询文件 `{st.session_state.get('file_chat_id')}` ...",
            ])
        else:
            client = openai.Client(base_url=f"{api_url}/knowledge_base/search_engine/{search_engine}", api_key="NONE")
            chat_box.ai_say([
                Markdown("...", in_expander=True, title="知识库匹配结果", state="running", expanded=st.session_state.return_direct),
                f"正在执行 `{search_engine}` 搜索...",
            ])

        text = ""
        first = True
        print(3)

        try:
            for d in client.chat.completions.create(messages=messages, model=llm_model, stream=True,
                                                    extra_body=extra_body):
                print(1)

                if first:
                    chat_box.update_msg("\n\n".join(d.docs), element_index=0, streaming=False, state="complete")
                    chat_box.update_msg("", streaming=False)
                    first = False
                    continue
                text += d.choices[0].delta.content or ""
                chat_box.update_msg(text.replace("\n", "\n\n"), streaming=True)
            chat_box.update_msg(text, streaming=False)
            # TODO: 搜索未配置API KEY时产生报错
        except Exception as e:
            st.error(e.body)